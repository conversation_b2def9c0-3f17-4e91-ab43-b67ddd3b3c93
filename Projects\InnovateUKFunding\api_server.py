#!/usr/bin/env python3
"""
API Server for Funding Opportunities Dashboard
Provides REST API endpoints to run scrapers and retrieve data
"""

import json
import os
import threading
import time
from datetime import datetime
from pathlib import Path
from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS

# Import our scrapers and data manager
from iuk_scraper import InnovateUKScraper
from net_zero_tender_finder import NetZeroTenderFinder
from funding_data_manager import FundingDataManager

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Global variables for tracking scraper status
scraper_status = {
    'iuk': {'status': 'idle', 'last_run': None, 'count': 0},
    'tender': {'status': 'idle', 'last_run': None, 'count': 0}
}

data_manager = FundingDataManager()
latest_data_file = None

@app.route('/')
def index():
    """Serve the main dashboard"""
    return send_from_directory('.', 'enhanced_dashboard_with_review.html')

@app.route('/api/status')
def get_status():
    """Get current scraper status"""
    return jsonify(scraper_status)

@app.route('/api/run-iuk-scraper', methods=['POST'])
def run_iuk_scraper():
    """Run the Innovate UK scraper"""
    if scraper_status['iuk']['status'] == 'running':
        return jsonify({'error': 'IUK scraper is already running'}), 400
    
    def run_scraper():
        try:
            scraper_status['iuk']['status'] = 'running'
            scraper = InnovateUKScraper()
            
            print("Starting IUK scraper...")
            opportunities = scraper.scrape_all(include_details=True)
            
            scraper_status['iuk']['status'] = 'success'
            scraper_status['iuk']['last_run'] = datetime.now().isoformat()
            scraper_status['iuk']['count'] = len(opportunities)
            
            # Save individual results
            scraper.save_to_json(opportunities, "iuk_opportunities_latest.json")
            scraper.save_to_csv(opportunities, "iuk_opportunities_latest.csv")
            
            print(f"IUK scraper completed: {len(opportunities)} opportunities")
            
        except Exception as e:
            scraper_status['iuk']['status'] = 'error'
            scraper_status['iuk']['error'] = str(e)
            print(f"IUK scraper error: {e}")
    
    # Run in background thread
    thread = threading.Thread(target=run_scraper)
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': 'IUK scraper started'})

@app.route('/api/run-tender-scraper', methods=['POST'])
def run_tender_scraper():
    """Run the Net Zero tender scraper"""
    if scraper_status['tender']['status'] == 'running':
        return jsonify({'error': 'Tender scraper is already running'}), 400
    
    def run_scraper():
        try:
            scraper_status['tender']['status'] = 'running'
            finder = NetZeroTenderFinder()
            
            print("Starting tender scraper...")
            opportunities = finder.search_opportunities(days_back=14, max_results=500)
            
            scraper_status['tender']['status'] = 'success'
            scraper_status['tender']['last_run'] = datetime.now().isoformat()
            scraper_status['tender']['count'] = len(opportunities)
            
            # Save individual results
            finder.save_to_csv(opportunities, "tender_opportunities_latest.csv")
            
            # Also save as JSON
            tender_data = []
            for opp in opportunities:
                if hasattr(opp, '__dict__'):
                    tender_data.append(opp.__dict__)
                else:
                    tender_data.append(opp)
            
            with open("tender_opportunities_latest.json", 'w', encoding='utf-8') as f:
                json.dump(tender_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"Tender scraper completed: {len(opportunities)} opportunities")
            
        except Exception as e:
            scraper_status['tender']['status'] = 'error'
            scraper_status['tender']['error'] = str(e)
            print(f"Tender scraper error: {e}")
    
    # Run in background thread
    thread = threading.Thread(target=run_scraper)
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': 'Tender scraper started'})

@app.route('/api/run-all-scrapers', methods=['POST'])
def run_all_scrapers():
    """Run both scrapers and combine the results"""
    if (scraper_status['iuk']['status'] == 'running' or 
        scraper_status['tender']['status'] == 'running'):
        return jsonify({'error': 'One or more scrapers are already running'}), 400
    
    def run_all():
        global latest_data_file
        try:
            # Set both to running
            scraper_status['iuk']['status'] = 'running'
            scraper_status['tender']['status'] = 'running'
            
            print("Starting combined scraper run...")
            
            # Run IUK scraper
            iuk_scraper = InnovateUKScraper()
            iuk_opportunities = iuk_scraper.scrape_all(include_details=True)
            
            # Run tender scraper
            tender_finder = NetZeroTenderFinder()
            tender_opportunities = tender_finder.search_opportunities(days_back=14, max_results=500)
            
            # Convert and combine data
            combined_opportunities = []
            
            # Convert IUK opportunities
            for iuk_opp in iuk_opportunities:
                try:
                    unified_opp = data_manager.convert_iuk_opportunity(iuk_opp)
                    combined_opportunities.append(unified_opp)
                except Exception as e:
                    print(f"Warning: Failed to convert IUK opportunity: {e}")
            
            # Convert tender opportunities
            for tender_opp in tender_opportunities:
                try:
                    unified_opp = data_manager.convert_tender_opportunity(tender_opp)
                    combined_opportunities.append(unified_opp)
                except Exception as e:
                    print(f"Warning: Failed to convert tender opportunity: {e}")
            
            # Save combined data
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_file, csv_file = data_manager.save_opportunities(
                combined_opportunities, 
                f"combined_opportunities_{timestamp}"
            )
            
            # Update latest data file reference
            latest_data_file = json_file
            
            # Update status
            scraper_status['iuk']['status'] = 'success'
            scraper_status['iuk']['last_run'] = datetime.now().isoformat()
            scraper_status['iuk']['count'] = len(iuk_opportunities)
            
            scraper_status['tender']['status'] = 'success'
            scraper_status['tender']['last_run'] = datetime.now().isoformat()
            scraper_status['tender']['count'] = len(tender_opportunities)
            
            # Create status file for the dashboard
            status_data = {
                "last_updated": datetime.now().isoformat(),
                "total_opportunities": len(combined_opportunities),
                "iuk_count": len(iuk_opportunities),
                "tender_count": len(tender_opportunities),
                "sustainable_count": sum(1 for opp in combined_opportunities if opp.is_sustainable),
                "json_file": json_file,
                "csv_file": csv_file
            }
            
            with open("scraper_status.json", "w") as f:
                json.dump(status_data, f, indent=2)
            
            print(f"Combined scraper run completed: {len(combined_opportunities)} total opportunities")
            
        except Exception as e:
            scraper_status['iuk']['status'] = 'error'
            scraper_status['tender']['status'] = 'error'
            scraper_status['iuk']['error'] = str(e)
            scraper_status['tender']['error'] = str(e)
            print(f"Combined scraper error: {e}")
    
    # Run in background thread
    thread = threading.Thread(target=run_all)
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': 'Combined scraper run started'})

@app.route('/api/get-latest-data')
def get_latest_data():
    """Get the latest scraped data"""
    global latest_data_file
    
    # Try to find the most recent data file
    if not latest_data_file:
        # Look for the most recent combined opportunities file
        data_files = list(Path('.').glob('combined_opportunities_*.json'))
        if data_files:
            latest_data_file = str(max(data_files, key=os.path.getctime))
    
    if latest_data_file and os.path.exists(latest_data_file):
        try:
            with open(latest_data_file, 'r', encoding='utf-8') as f:
                opportunities_data = json.load(f)
            
            # Load status if available
            status_data = {}
            if os.path.exists('scraper_status.json'):
                with open('scraper_status.json', 'r') as f:
                    status_data = json.load(f)
            
            return jsonify({
                'opportunities': opportunities_data,
                'last_updated': status_data.get('last_updated'),
                'total_count': len(opportunities_data),
                'status': status_data
            })
            
        except Exception as e:
            return jsonify({'error': f'Failed to load data: {str(e)}'}), 500
    
    return jsonify({'error': 'No data available'}), 404

@app.route('/api/get-scraper-logs')
def get_scraper_logs():
    """Get scraper execution logs (placeholder)"""
    # In a real implementation, this would return actual log data
    return jsonify({
        'iuk_logs': ['IUK scraper log entries...'],
        'tender_logs': ['Tender scraper log entries...']
    })

@app.route('/api/run-smart-scrapers', methods=['POST'])
def run_smart_scrapers():
    """Run smart scrapers with enhanced features"""
    try:
        config = request.get_json() or {}
        
        # Import enhanced scrapers
        from enhanced_scrapers import run_smart_scrapers
        
        iuk_data, nz_data = run_smart_scrapers(
            force_iuk_rescrape=config.get('force_iuk_rescrape', False),
            human_review=config.get('enable_human_review', False),
            min_relevance_score=config.get('min_relevance_score', 30.0),
            auto_accept_threshold=config.get('auto_accept_threshold', 70.0)
        )
        
        return jsonify({
            'success': True,
            'message': f'Smart scraping completed! IUK: {len(iuk_data)}, Net Zero: {len(nz_data)}',
            'iuk_count': len(iuk_data),
            'nz_count': len(nz_data),
            'new_count': len(iuk_data) + len(nz_data)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Smart scraping failed: {str(e)}'
        }), 500

@app.route('/api/run-iuk-smart', methods=['POST'])
def run_iuk_smart():
    """Run IUK scraper with smart date checking"""
    try:
        config = request.get_json() or {}
        
        from enhanced_scrapers import EnhancedIUKScraper
        scraper = EnhancedIUKScraper()
        
        opportunities = scraper.smart_scrape(
            force_rescrape=config.get('force_rescrape', False)
        )
        
        if not opportunities:
            message = "IUK scraping skipped - already scraped today"
        else:
            message = f"IUK scraper completed: {len(opportunities)} opportunities"
        
        return jsonify({
            'success': True,
            'message': message,
            'count': len(opportunities)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'IUK smart scraping failed: {str(e)}'
        }), 500

@app.route('/api/run-netzero-smart', methods=['POST'])
def run_netzero_smart():
    """Run Net Zero scraper with enhanced filtering"""
    try:
        config = request.get_json() or {}
        
        from enhanced_scrapers import EnhancedNetZeroFinder
        finder = EnhancedNetZeroFinder()
        
        opportunities_with_scores = finder.search_with_relevance_scoring(
            days_back=config.get('days_back', 14),
            max_results=500,
            min_relevance_score=config.get('min_relevance_score', 30.0)
        )
        
        opportunities = [opp for opp, score, details in opportunities_with_scores]
        
        # Optional LLM filtering
        if config.get('enable_llm', False):
            try:
                from llm_relevance_checker import LLMRelevanceChecker
                checker = LLMRelevanceChecker()
                opportunities, assessments = checker.assess_batch(
                    opportunities, 
                    confidence_threshold=60.0
                )
                message = f"Net Zero scraper with LLM completed: {len(opportunities)} relevant opportunities"
            except Exception as llm_error:
                message = f"Net Zero scraper completed: {len(opportunities)} opportunities (LLM filtering failed: {str(llm_error)})"
        else:
            message = f"Net Zero scraper completed: {len(opportunities)} opportunities"
        
        return jsonify({
            'success': True,
            'message': message,
            'count': len(opportunities)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Net Zero smart scraping failed: {str(e)}'
        }), 500

@app.route('/api/data-stats')
def get_data_stats():
    """Get statistics about the master data"""
    try:
        from funding_data_manager import FundingDataManager
        manager = FundingDataManager()
        
        opportunities, _ = manager.load_existing_opportunities()
        
        source_counts = {}
        for opp in opportunities:
            source_counts[opp.source] = source_counts.get(opp.source, 0) + 1
        
        sustainable_count = sum(1 for opp in opportunities if opp.is_sustainable)
        
        return jsonify({
            'total_opportunities': len(opportunities),
            'iuk_count': source_counts.get('innovate_uk', 0),
            'tender_count': source_counts.get('net_zero_tenders', 0),
            'sustainable_count': sustainable_count,
            'last_updated': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'total_opportunities': 0,
            'iuk_count': 0,
            'tender_count': 0,
            'sustainable_count': 0,
            'last_updated': None,
            'error': str(e)
        })

@app.route('/api/clean-titles', methods=['POST'])
def clean_titles():
    """Clean all titles in the master data"""
    try:
        from funding_data_manager import FundingDataManager
        manager = FundingDataManager()
        
        opportunities, _ = manager.load_existing_opportunities()
        
        cleaned_count = 0
        for opp in opportunities:
            original_title = opp.title
            cleaned_title = manager.clean_title_for_deduplication(opp.title)
            
            if cleaned_title != original_title and cleaned_title:
                opp.title = cleaned_title
                cleaned_count += 1
        
        if cleaned_count > 0:
            manager.save_master_data(opportunities)
        
        return jsonify({
            'success': True,
            'cleaned_count': cleaned_count,
            'message': f'Cleaned {cleaned_count} titles'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Title cleaning failed: {str(e)}'
        }), 500

@app.route('/api/reset-master-data', methods=['POST'])
def reset_master_data():
    """Reset the master data file"""
    try:
        from funding_data_manager import FundingDataManager
        manager = FundingDataManager()
        
        if manager.master_data_file.exists():
            backup_file = manager.data_dir / f"master_backup_before_reset_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            manager.master_data_file.rename(backup_file)
            
            return jsonify({
                'success': True,
                'message': f'Master data reset. Backup created: {backup_file.name}'
            })
        else:
            return jsonify({
                'success': True,
                'message': 'No master data file to reset'
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Reset failed: {str(e)}'
        }), 500

@app.route('/api/export-csv', methods=['POST'])
def export_csv():
    """Export current data to CSV"""
    try:
        from funding_data_manager import FundingDataManager
        manager = FundingDataManager()
        
        opportunities, _ = manager.load_existing_opportunities()
        
        if opportunities:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            json_file, csv_file = manager.save_opportunities(
                opportunities, 
                f"export_{timestamp}"
            )
            
            # Return the CSV file
            return send_from_directory('.', csv_file.split('/')[-1], as_attachment=True)
        else:
            return jsonify({
                'success': False,
                'message': 'No data to export'
            }), 404
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Export failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 Starting Funding Opportunities API Server")
    print("Dashboard available at: http://localhost:5000")
    print("API endpoints:")
    print("  POST /api/run-iuk-scraper")
    print("  POST /api/run-tender-scraper") 
    print("  POST /api/run-all-scrapers")
    print("  POST /api/run-smart-scrapers")
    print("  POST /api/run-iuk-smart")
    print("  POST /api/run-netzero-smart")
    print("  GET  /api/get-latest-data")
    print("  GET  /api/status")
    print("  GET  /api/data-stats")
    print("  POST /api/clean-titles")
    print("  POST /api/reset-master-data")
    print("  POST /api/export-csv")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
