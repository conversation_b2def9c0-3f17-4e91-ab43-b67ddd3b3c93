#!/usr/bin/env python3
"""
Combined Scrapers Runner
Runs both IUK and Net Zero tender scrapers and combines the results
"""

import json
import sys
from datetime import datetime
from pathlib import Path

# Import our scrapers and data manager
from iuk_scraper import InnovateUKScraper
from net_zero_tender_finder import NetZeroTenderFinder
from funding_data_manager import FundingDataManager, FundingOpportunity

def run_iuk_scraper(include_details=True, max_opportunities=None):
    """Run the Innovate UK scraper"""
    print("🔍 Running Innovate UK scraper...")
    scraper = InnovateUKScraper()
    
    try:
        opportunities = scraper.scrape_all(include_details=include_details, max_opportunities=max_opportunities)
        print(f"✅ IUK Scraper: Found {len(opportunities)} opportunities")
        return opportunities
    except Exception as e:
        print(f"❌ IUK Scraper failed: {e}")
        return []

def run_tender_scraper(days_back=14, max_results=500):
    """Run the Net Zero tender scraper"""
    print("🔍 Running Net Zero tender scraper...")
    finder = NetZeroTenderFinder()
    
    try:
        opportunities = finder.search_opportunities(days_back=days_back, max_results=max_results)
        print(f"✅ Tender Scraper: Found {len(opportunities)} opportunities")
        return opportunities
    except Exception as e:
        print(f"❌ Tender Scraper failed: {e}")
        return []

def combine_and_save_data(iuk_data, tender_data, output_prefix="combined_opportunities"):
    """Combine data from both scrapers and save in unified format"""
    print("🔄 Converting and combining data...")
    
    manager = FundingDataManager()
    combined_opportunities = []
    
    # Convert IUK opportunities
    for iuk_opp in iuk_data:
        try:
            unified_opp = manager.convert_iuk_opportunity(iuk_opp)
            combined_opportunities.append(unified_opp)
        except Exception as e:
            print(f"Warning: Failed to convert IUK opportunity: {e}")
    
    # Convert tender opportunities
    for tender_opp in tender_data:
        try:
            unified_opp = manager.convert_tender_opportunity(tender_opp)
            combined_opportunities.append(unified_opp)
        except Exception as e:
            print(f"Warning: Failed to convert tender opportunity: {e}")
    
    # Use the new deduplication system
    print("🔍 Checking for duplicates against existing data...")
    truly_new_opportunities, new_count, duplicate_count = manager.add_new_opportunities(combined_opportunities)
    
    # Save timestamped backup of all processed data (including duplicates for reference)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_file, csv_file = manager.save_opportunities(
        combined_opportunities, 
        f"{output_prefix}_{timestamp}"
    )
    
    return truly_new_opportunities, json_file, csv_file

def print_summary(opportunities):
    """Print a summary of the combined opportunities"""
    if not opportunities:
        print("No opportunities found.")
        return
    
    print(f"\n📊 COMBINED SUMMARY")
    print(f"=" * 50)
    print(f"Total opportunities: {len(opportunities)}")
    
    # By source
    source_counts = {}
    for opp in opportunities:
        source_counts[opp.source] = source_counts.get(opp.source, 0) + 1
    
    print(f"\nBy source:")
    for source, count in source_counts.items():
        source_name = "Innovate UK" if source == "innovate_uk" else "Net Zero Tenders"
        print(f"  {source_name}: {count}")
    
    # By sustainability
    sustainable_count = sum(1 for opp in opportunities if opp.is_sustainable)
    print(f"\nSustainability-related: {sustainable_count} ({sustainable_count/len(opportunities)*100:.1f}%)")
    
    # By funding category
    funding_counts = {}
    for opp in opportunities:
        category = opp.funding_category or "No amount specified"
        funding_counts[category] = funding_counts.get(category, 0) + 1
    
    print(f"\nBy funding category:")
    for category, count in sorted(funding_counts.items()):
        print(f"  {category}: {count}")
    
    # Recent opportunities (with closing dates)
    recent_opps = [opp for opp in opportunities if opp.closes_date]
    if recent_opps:
        print(f"\n🕒 UPCOMING DEADLINES (Top 5)")
        print(f"-" * 30)
        # Sort by closing date
        try:
            recent_opps.sort(key=lambda x: x.closes_date if x.closes_date else "9999-12-31")
            for i, opp in enumerate(recent_opps[:5], 1):
                source_name = "IUK" if opp.source == "innovate_uk" else "Tender"
                print(f"{i}. [{source_name}] {opp.title[:60]}...")
                print(f"   Closes: {opp.closes_date}")
                if opp.funding_category:
                    print(f"   Funding: {opp.funding_category}")
                print()
        except Exception as e:
            print(f"Error sorting by date: {e}")

def main():
    """Main function to run both scrapers and combine results"""
    print("🌱 Combined Funding Opportunity Scraper")
    print("=" * 60)
    
    # Configuration
    iuk_include_details = True
    iuk_max_opportunities = None  # No limit
    tender_days_back = 14
    tender_max_results = 500
    
    # Run scrapers
    iuk_data = run_iuk_scraper(iuk_include_details, iuk_max_opportunities)
    tender_data = run_tender_scraper(tender_days_back, tender_max_results)
    
    if not iuk_data and not tender_data:
        print("❌ No data found from either scraper. Exiting.")
        return
    
    # Combine and save (with deduplication)
    new_opportunities, json_file, csv_file = combine_and_save_data(iuk_data, tender_data)
    
    # Print summary
    print_summary(new_opportunities)
    
    print(f"\n💾 Files saved:")
    print(f"  JSON: {json_file}")
    print(f"  CSV: {csv_file}")
    
    # Load master data to get total counts
    manager = FundingDataManager()
    all_opportunities, _ = manager.load_existing_opportunities()
    
    # Create a simple summary file for the web interface
    summary_data = {
        "last_updated": datetime.now().isoformat(),
        "total_opportunities": len(all_opportunities),
        "new_opportunities_this_run": len(new_opportunities),
        "iuk_scraped": len(iuk_data),
        "tender_scraped": len(tender_data),
        "sustainable_count": sum(1 for opp in all_opportunities if opp.is_sustainable),
        "json_file": json_file,
        "csv_file": csv_file,
        "master_data_file": str(manager.master_data_file)
    }
    
    with open("scraper_status.json", "w") as f:
        json.dump(summary_data, f, indent=2)
    
    print(f"\n✅ Scraping completed successfully!")
    print(f"📈 Found {len(new_opportunities)} new opportunities (out of {len(iuk_data) + len(tender_data)} scraped)")
    print(f"📊 Total opportunities in database: {len(all_opportunities)}")
    print(f"📄 Status file created: scraper_status.json")
    
    if len(new_opportunities) == 0:
        print("ℹ️  No new opportunities found - all scraped opportunities were already in the database")

if __name__ == "__main__":
    main()
