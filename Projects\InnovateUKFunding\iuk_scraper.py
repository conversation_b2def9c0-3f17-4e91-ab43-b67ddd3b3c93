import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import re
from urllib.parse import urljoin, urlparse
import json
from datetime import datetime

class InnovateUKScraper:
    def __init__(self, base_url="https://iuk-business-connect.org.uk/opportunities/"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.pagination_format = None

    def get_page(self, url, retries=3, delay=1):
        for attempt in range(retries):
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                print(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt < retries - 1:
                    time.sleep(delay)
                else:
                    print(f"Failed to fetch {url} after {retries} attempts")
                    return None

    def clean_title(self, title):
        """Clean and improve title formatting, removing concatenated content"""
        if not title:
            return "Untitled Opportunity"
        
        # Remove HTML tags
        title = re.sub(r'<[^>]+>', '', title)
        
        # Remove extra whitespace
        title = re.sub(r'\s+', ' ', title).strip()
        
        # Store original for fallback
        original_title = title
        
        # Strategy: Find the first substantial part before contamination
        # Look for "Opens" as a clear delimiter
        opens_match = re.search(r'^(.*?)(?=Opens\s+\d{2}/\d{2}/\d{4})', title, flags=re.IGNORECASE)
        if opens_match:
            title = opens_match.group(1).strip()
        else:
            # Try other common delimiters
            delimiters = [
                r'^(.*?)(?=\d{2}/\d{2}/\d{4})',  # Before any date
                r'^(.*?)(?=More Information)',    # Before "More Information"
                r'^(.*?)(?=Read More)',          # Before "Read More"
                r'^(.*?)(?=Apply Now)',          # Before "Apply Now"
                r'^(.*?)(?=View Details)',       # Before "View Details"
            ]
            
            for delimiter in delimiters:
                match = re.search(delimiter, title, flags=re.IGNORECASE)
                if match and len(match.group(1).strip()) > 10:  # Only if we get substantial content
                    title = match.group(1).strip()
                    break
        
        # If title is still empty or too short, try to extract the first meaningful sentence
        if not title or len(title) < 10:
            # Look for a sentence that ends with typical title patterns
            sentence_patterns = [
                r'^([^.!?]*(?:call|programme|program|initiative|opportunity|challenge|fund|grant)[^.!?]*)',
                r'^([A-Z][^-]*-[^-]*)',  # Pattern like "Open Horizons - Something"
                r'^([^.!?]{20,100})',     # First 20-100 characters before punctuation
            ]
            
            for pattern in sentence_patterns:
                match = re.search(pattern, original_title, flags=re.IGNORECASE)
                if match:
                    potential_title = match.group(1).strip()
                    # Ensure it doesn't contain obvious description content
                    if not re.search(r'(can apply for|eligible|startups? in|companies? in)', potential_title, flags=re.IGNORECASE):
                        title = potential_title
                        break
        
        # Clean up the extracted title
        if title:
            # Remove trailing punctuation artifacts
            title = re.sub(r'[:\s]*$', '', title)
            
            # Remove common redundant phrases at the beginning
            title = re.sub(r'^(opportunity[:\s]*|funding[:\s]*|grant[:\s]*)', '', title, flags=re.IGNORECASE)
            
            # Clean up URL slug patterns if it looks like one
            if len(title) < 20 and ('-' in title or '_' in title) and title.islower():
                title = title.replace('-', ' ').replace('_', ' ').title()
            
            # Final cleanup
            title = re.sub(r'\s+', ' ', title).strip()
            
            # Ensure first letter is capitalized
            if title and title[0].islower():
                title = title[0].upper() + title[1:]
        
        # Final validation - if we still have nothing meaningful, use fallback
        if not title or len(title) < 5:
            # Try one more time with a very simple approach - just take first few words
            words = original_title.split()
            if len(words) >= 3:
                title = ' '.join(words[:8])  # Take first 8 words max
                # Remove if it ends with obvious date/description starters
                title = re.sub(r'\s+(opens?|closes?|more|read|view|apply|can|women|men|startups?|companies?).*$', '', title, flags=re.IGNORECASE)
            else:
                title = "Untitled Opportunity"
        
        return title if title and len(title) >= 5 else "Untitled Opportunity"

    def extract_title_from_link(self, link_element):
        """Extract just the title from a link element, avoiding concatenated content"""
        
        # Method 1: Look for specific title elements within the link
        title_selectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',  # Headings
            '.title', '.heading', '.name',         # Common title classes
            '[class*="title"]', '[class*="heading"]'  # Classes containing title/heading
        ]
        
        for selector in title_selectors:
            title_elem = link_element.select_one(selector)
            if title_elem:
                return title_elem.get_text(strip=True)
        
        # Method 2: If no specific title element, try to get the first meaningful text
        # Look for the first substantial text node or span
        text_elements = link_element.find_all(['span', 'div', 'p'], string=True)
        for elem in text_elements:
            text = elem.get_text(strip=True)
            # Skip if it looks like a date, button text, or very short
            if (text and len(text) > 10 and 
                not re.search(r'\d{2}/\d{2}/\d{4}', text) and  # Not a date
                not re.search(r'^(opens?|closes?|more|read|view|apply)$', text, re.IGNORECASE) and  # Not button text
                not re.search(r'^\d+\s*(days?|hours?|minutes?)?\s*(ago|left|remaining)$', text, re.IGNORECASE)):  # Not time remaining
                return text
        
        # Method 3: Fallback - get all text but try to extract just the first sentence/phrase
        full_text = link_element.get_text(strip=True)
        if full_text:
            # Split on common separators and take the first meaningful part
            parts = re.split(r'(?:Opens|Closes|More Information|Read More|Apply|View)', full_text, flags=re.IGNORECASE)
            if parts and parts[0].strip():
                first_part = parts[0].strip()
                # If it's still very long, try to split on sentences
                if len(first_part) > 100:
                    sentences = re.split(r'[.!?]+', first_part)
                    if sentences and len(sentences[0].strip()) > 10:
                        return sentences[0].strip()
                return first_part
        
        return full_text or "Untitled Opportunity"

    def parse_datetime(self, date_text):
        if not date_text:
            return None
        date_text = re.sub(r'\s+', ' ', date_text.strip())
        patterns = [
            r'(\d{2}/\d{2}/\d{4})\s+(\d{2}:\d{2})',
            r'(\d{2}/\d{2}/\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
        ]
        for pattern in patterns:
            match = re.search(pattern, date_text)
            if match:
                try:
                    if len(match.groups()) == 2:
                        return datetime.strptime(f"{match.group(1)} {match.group(2)}", '%d/%m/%Y %H:%M')
                    else:
                        return datetime.strptime(match.group(1), '%d/%m/%Y')
                except ValueError:
                    continue
        return date_text

    def extract_funding_info(self, text):
        funding_info = {
            'funding_amount': None,
            'max_award': None,
            'currency': None,
            'funding_category': None
        }
        patterns = [
            r'[£€$]\s*[\d,]+(?:\.\d+)?[kmb]?(?:\s*million|\s*thousand|)',
            r'up to [£€$]\s*[\d,]+(?:\.\d+)?[kmb]?',
            r'share of up to [£€$]\s*[\d,]+(?:\.\d+)?[kmb]?',
            r'€\s*[\d,]+(?:\.\d+)?[k]?'
        ]
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                funding_info['funding_amount'] = matches[0]
                currency_match = re.search(r'[£€$]', matches[0])
                if currency_match:
                    funding_info['currency'] = currency_match.group(0)

                # Calculate funding category
                funding_info['funding_category'] = self.categorize_funding_amount(matches[0])
                break
        return funding_info

    def categorize_funding_amount(self, amount_str):
        """Categorize funding amount into standard ranges, handling multiple currencies"""
        if not amount_str:
            return "No amount specified"

        # Extract numeric value and currency
        currency_match = re.search(r'[£€$]', amount_str)
        currency = currency_match.group(0) if currency_match else '£'

        # Extract numeric value
        numeric_match = re.search(r'[\d,]+(?:\.\d+)?', amount_str.replace(',', ''))
        if not numeric_match:
            return "No amount specified"

        try:
            value = float(numeric_match.group(0).replace(',', ''))
        except ValueError:
            return "No amount specified"

        # Handle multipliers (k, m, b, million, thousand)
        amount_lower = amount_str.lower()
        if 'million' in amount_lower or 'm' in amount_lower:
            value *= 1_000_000
        elif 'thousand' in amount_lower or 'k' in amount_lower:
            value *= 1_000
        elif 'billion' in amount_lower or 'b' in amount_lower:
            value *= 1_000_000_000

        # Convert to GBP equivalent for categorization (approximate rates)
        if currency == '$':
            value *= 0.79  # USD to GBP
        elif currency == '€':
            value *= 0.85  # EUR to GBP

        # Categorize based on GBP equivalent
        if value >= 10_000_000:
            return "Over £10M"
        elif value >= 1_000_000:
            return "£1M - £10M"
        elif value >= 100_000:
            return "£100k - £1M"
        elif value > 0:
            return "Under £100k"
        else:
            return "No amount specified"

    def scrape_opportunities_list(self):
        print("Fetching opportunities list...")
        all_opportunities = []
        page = 1
        max_pages = 5 # Limited to focus on current opportunities, avoiding closed funding
        previous_page_opportunities_count = -1 # To detect end of unique content

        while page <= max_pages:
            url_to_fetch = self.base_url
            if page > 1:
                # Use the determined pagination format or try variants
                if self.pagination_format:
                    url_to_fetch = self.pagination_format.format(page)
                else:
                    url_variants = [
                        f"{self.base_url}?sf_paged={page}",  # SearchAndFilter plugin pagination (most likely)
                        f"{self.base_url}?page={page}",
                        f"{self.base_url}page/{page}/",
                        f"{self.base_url}?paged={page}", # Common WordPress pagination
                        f"{self.base_url}{page}/",
                    ]
                    found_variant = False
                    for variant_url in url_variants:
                        response_check = self.get_page(variant_url)
                        if response_check and response_check.status_code == 200:
                            # Heuristic: Check for existence of expected content on the page
                            if "opportunity" in response_check.text.lower() or "challenge" in response_check.text.lower():
                                self.pagination_format = variant_url.replace(str(page), "{}")
                                url_to_fetch = variant_url
                                found_variant = True
                                break
                    if not found_variant:
                        print(f"Could not find a working pagination format for page {page}. Stopping.")
                        break

            print(f"Fetching page {page}: {url_to_fetch}")
            response = self.get_page(url_to_fetch)

            if not response:
                print(f"Failed to fetch page {page}")
                break

            soup = BeautifulSoup(response.content, 'html.parser')
            page_opportunities = []

            # Method 1: Look for actual HTML links first within common content areas
            # print(f"  Trying Method 1 on page {page}: HTML links...") # Kept for debugging if needed
            listing_containers = soup.find_all(['div', 'ul', 'ol'], class_=re.compile(r'opportunity|listing|grid|cards|result', re.IGNORECASE))
            if not listing_containers:
                listing_containers = [soup] # Fallback to entire soup

            for container in listing_containers:
                links = container.find_all('a', href=True)
                for link in links:
                    href = link.get('href', '')
                    full_url = urljoin(self.base_url, href)

                    if '/opportunities/' in full_url and full_url != self.base_url.rstrip('/') and urlparse(full_url).netloc == urlparse(self.base_url).netloc:
                        # Refined check for opportunity specific URLs
                        if re.search(r'/opportunities/[-\w]+/?$', full_url): # Matches slugs like /opportunities/some-slug/
                            # Try to extract just the title part, not all link content
                            title = self.extract_title_from_link(link)
                            title = self.clean_title(title)
                            
                            # Skip if title is too short or generic (likely navigation)
                            if len(title) < 5 or "read more" in title.lower():
                                continue

                            brief_description_elem = link.find_next(['p', 'div'], class_=re.compile(r'description|summary|excerpt|content', re.IGNORECASE))
                            brief_description = brief_description_elem.get_text(strip=True) if brief_description_elem else ''

                            context_text = link.find_parent().get_text(separator=' ', strip=True) if link.find_parent() else ""
                            opens_date_match = re.search(r'Opens:?\s*(\d{1,2}/\d{1,2}/\d{4})', context_text)
                            closes_date_match = re.search(r'Closes:?\s*(\d{1,2}/\d{1,2}/\d{4})', context_text)
                            
                            opens_date = self.parse_datetime(opens_date_match.group(1)) if opens_date_match else None
                            closes_date = self.parse_datetime(closes_date_match.group(1)) if closes_date_match else None

                            # Only add if not already collected across all pages
                            if full_url not in [opp['url'] for opp in all_opportunities]:
                                opportunity = {
                                    'title': title,
                                    'url': full_url,
                                    'brief_description': brief_description[:500],
                                    'opens_date': opens_date,
                                    'closes_date': closes_date,
                                    'funding_amount': None,
                                    'currency': None,
                                    'funding_category': None,
                                    'page_found': page
                                }
                                page_opportunities.append(opportunity)
                                # print(f"    Found (M1): {title[:60]}...") # Kept for debugging if needed

            # Method 2: Fallback text parsing for URLs if Method 1 is insufficient
            if not page_opportunities or len(page_opportunities) < 5: # If very few or no opportunities found by M1
                # print(f"  Trying Method 2 on page {page}: Text pattern matching...") # Kept for debugging if needed
                html_content = response.text
                url_pattern = r'https://iuk-business-connect\.org\.uk/opportunities/[^"\s\)]+[^"\s\)\.]'
                urls = re.findall(url_pattern, html_content)

                unique_urls = []
                seen_urls_current_page = set()
                for url in urls:
                    clean_url = re.sub(r'["\)\]]+$', '', url)
                    if clean_url not in seen_urls_current_page and clean_url != self.base_url.rstrip('/'):
                        if not any(opp['url'] == clean_url for opp in all_opportunities):
                            seen_urls_current_page.add(clean_url)
                            unique_urls.append(clean_url)

                for url in unique_urls:
                    url_escaped = re.escape(url)
                    context_pattern_before = rf'(.{{0,1000}}){url_escaped}'
                    context_match_before = re.search(context_pattern_before, html_content, re.DOTALL)
                    context_before = context_match_before.group(1) if context_match_before else ""

                    context_pattern_after = rf'{url_escaped}(.{{0,1000}})'
                    context_match_after = re.search(context_pattern_after, html_content, re.DOTALL)
                    context_after = context_match_after.group(1) if context_match_after else ""

                    combined_context = (context_before + " " + context_after).strip()
                    combined_context = re.sub(r'\s+', ' ', combined_context)

                    title_match_heading = re.search(r'<h[1-3][^>]*>(.*?)</h[1-3]>.*?%s' % url_escaped, html_content, re.IGNORECASE | re.DOTALL)
                    title_match_markdown = re.search(r'\[([^\]]+)\]\(%s\)' % url_escaped, html_content, re.IGNORECASE | re.DOTALL)
                    
                    title = ""
                    if title_match_heading:
                        title = title_match_heading.group(1).strip()
                    elif title_match_markdown:
                        title = title_match_markdown.group(1).strip()
                    else:
                        title_parts = [p for p in url.split('/') if p and p != 'opportunities']
                        if title_parts:
                            title = title_parts[-1]
                        else:
                            title = "Untitled Opportunity"
                    
                    # Clean and improve the title
                    title = self.clean_title(title)
                    
                    if len(title) < 15 and "read more" in title.lower(): # Still filter generic titles
                        continue

                    brief_description = ""
                    desc_search = re.search(r'(?:<p>|\.)\s*([^.<>]+[.?!])', combined_context, re.DOTALL)
                    if desc_search:
                        brief_description = desc_search.group(1).strip()
                    else:
                        brief_description = combined_context[:200]

                    opens_date = None
                    closes_date = None
                    opens_match = re.search(r'Opens:?\s*(\d{1,2}/\d{1,2}/\d{4})', combined_context)
                    closes_match = re.search(r'Closes:?\s*(\d{1,2}/\d{1,2}/\d{4})', combined_context)

                    if opens_match:
                        opens_date = self.parse_datetime(opens_match.group(1))
                    if closes_match:
                        closes_date = self.parse_datetime(closes_match.group(1))

                    funding_info = self.extract_funding_info(combined_context)

                    opportunity = {
                        'title': title.strip(),
                        'url': url,
                        'brief_description': brief_description[:500],
                        'opens_date': opens_date,
                        'closes_date': closes_date,
                        'funding_amount': funding_info['funding_amount'],
                        'currency': funding_info['currency'],
                        'funding_category': funding_info['funding_category'],
                        'page_found': page
                    }
                    if not any(opp['url'] == opportunity['url'] for opp in page_opportunities):
                        page_opportunities.append(opportunity)
                        # print(f"    Found (M2): {title[:60]}...") # Kept for debugging if needed

            current_page_unique_count = len(page_opportunities)

            # Check for end of pagination: if no new opportunities are found
            if current_page_unique_count == 0 and page > 1:
                print(f"No new opportunities found on page {page}. Assuming end of pagination.")
                break
            
            # This condition helps if the last page duplicates content from previous pages or is empty
            # Only stop if we've had multiple pages with 0 new opportunities
            if current_page_unique_count == 0 and previous_page_opportunities_count == 0 and page > 2:
                print(f"No opportunities found on pages {page-1} and {page}. Assuming end of pagination.")
                break

            # If we're on page 1 and found nothing
            if current_page_unique_count == 0 and page == 1:
                print("No opportunities found on page 1. Debugging information:")
                print("Raw text sample (first 1000 chars):")
                print(soup.get_text()[:1000])
                print("\nHTML sample (first 1000 chars):")
                print(response.text[:1000])
                # Try to find ANY opportunity-related URLs as a last resort
                all_urls = re.findall(r'https?://[^\s<>"]+', response.text)
                opp_urls = [url for url in all_urls if 'opportunities' in url and urlparse(url).netloc == urlparse(self.base_url).netloc]
                print(f"\nFound {len(opp_urls)} URLs containing 'opportunities':")
                for url in opp_urls[:5]:
                    print(f"  {url}")
                break

            all_opportunities.extend(page_opportunities)
            print(f"Page {page}: Added {len(page_opportunities)} opportunities. Total unique so far: {len(all_opportunities)}")

            previous_page_opportunities_count = current_page_unique_count
            page += 1
            time.sleep(1)

        print(f"Finished scraping list. Total unique opportunities found: {len(all_opportunities)}")
        return all_opportunities

    def extract_structured_details(self, soup):
        details = {
            'organisation': None,
            'registration_opens': None,
            'registration_closes': None,
            'award_info': None,
            'background': None,
            'challenge_description': None,
            'requirements': [],
            'assessment_criteria': [],
            'timeline': [],
            'application_process': None,
            'ip_arrangements': None,
            'detailed_funding_amount': None,
            'max_award': None,
            'detailed_currency': None,
            'detailed_funding_category': None,
            'programme_type': None,
            'sectors': [],
            'eligibility': None,
            'contact_info': None
        }

        full_text = soup.get_text(separator=' ', strip=True)
        
        funding_info = self.extract_funding_info(full_text)
        details['detailed_funding_amount'] = funding_info['funding_amount']
        details['detailed_currency'] = funding_info['currency']
        details['detailed_funding_category'] = funding_info['funding_category']

        sections = {}
        current_section = None
        # Iterate through children, capturing text associated with headings
        for element in soup.find('body').findChildren(recursive=False): # Only direct children of body for top-level sections
            if element.name and element.name.startswith('h') and element.name in ['h2', 'h3', 'h4']:
                current_section_title = element.get_text(strip=True).lower().replace(':', '').strip()
                current_section = current_section_title
                sections[current_section] = []
            elif current_section is not None:
                if isinstance(element, (str, BeautifulSoup.Tag)):
                    if isinstance(element, str):
                        sections[current_section].append(element.strip())
                    else:
                        sections[current_section].append(element.get_text(separator=' ', strip=True))

        for key in sections:
            sections[key] = ' '.join(sections[key]).strip()

        # Extract specific structured data using collected sections or full_text
        for pattern_keys, field_name, length_limit, split_char in [
            (['organisation', 'funder', 'funded by'], 'organisation', 200, None),
            (['award', 'benefits', 'what you get'], 'award_info', 500, None),
            (['background'], 'background', 1000, None),
            (['the challenge', 'challenge', 'problem', 'opportunity'], 'challenge_description', 1000, None),
            (['requirements', 'must', 'should'], 'requirements', 200, r'[-•·\n]\s*'),
            (['assessment criteria', 'judging criteria', 'assessed on', 'selection criteria', 'evaluation'], 'assessment_criteria', 200, r'[-•·\n]\s*'),
            (['application process', 'how to apply', 'to apply', 'apply'], 'application_process', 500, None),
            (['intellectual property', 'ip', 'patents', 'ownership', 'rights'], 'ip_arrangements', 500, None),
            (['eligibility', 'who can apply'], 'eligibility', 300, None),
            (['contact us', 'further information', 'enquiries', 'email', 'phone'], 'contact_info', 300, None)
        ]:
            if field_name not in details or not details[field_name]: # Only fill if not already set
                found = False
                for pk in pattern_keys:
                    if pk in sections and sections[pk]:
                        text_content = sections[pk]
                        if split_char:
                            items = [item.strip()[:length_limit] for item in re.split(split_char, text_content) if item.strip()]
                            details[field_name] = items[:10] # Limit to top 10 items for lists
                        else:
                            details[field_name] = text_content[:length_limit]
                        found = True
                        break
                
                # Fallback to general regex search on full_text if not found in sections
                if not found:
                    if field_name == 'organisation':
                        org_match = re.search(r'(?:Organisation|Funder|Funded by)[:\s]*(.*?)(?:\n|$)', full_text, re.IGNORECASE)
                        if org_match: details[field_name] = org_match.group(1).strip()[:length_limit]
                    elif field_name == 'award_info':
                        award_patterns = [r'Award[:\s]*(.*?)(?=Organisation|Background|Challenge|When|Eligibility|$)', r'Benefits[:\s]*(.*?)(?=Organisation|Background|Challenge|When|Eligibility|$)', r'What you get[:\s]*(.*?)(?=Organisation|Background|Challenge|When|Eligibility|$)' ]
                        for p in award_patterns:
                            m = re.search(p, full_text, re.IGNORECASE | re.DOTALL)
                            if m: details[field_name] = m.group(1).strip()[:length_limit]; break
                    # Add other fallbacks if needed, using similar structure as original code

        # Date parsing (often not under a clear "heading" so specific regex is good)
        reg_opens_match = re.search(r'Registration Opens[:\s]*(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2})', full_text)
        if reg_opens_match:
            details['registration_opens'] = self.parse_datetime(reg_opens_match.group(1))
        
        reg_closes_match = re.search(r'Registration Closes[:\s]*(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2})', full_text)
        if reg_closes_match:
            details['registration_closes'] = self.parse_datetime(reg_closes_match.group(1))

        # Timeline (often list-like, so section-based or specific pattern)
        if not details['timeline']:
            timeline_patterns = [
                r'(\d{1,2}(?:st|nd|rd|th)?\s*[A-Za-z]+|\d{1,2}/\d{1,2}/\d{4})[:\s]*([^\n]+)',
                r'([A-Za-z]+ \d{4})[:\s]*([^\n]+)'
            ]
            timeline_items = []
            for pattern in timeline_patterns:
                timeline_dates = re.findall(pattern, full_text)
                timeline_items.extend([f"{date}: {event[:100]}" for date, event in timeline_dates[:3]])
            details['timeline'] = timeline_items[:5]

        # Programme type identification (should always be from full_text)
        programme_indicators = {
            'Innovation Exchange': ['innovation exchange', 'ix programme'],
            'Global Business Innovation Programme': ['global business innovation', 'gbip'],
            'Accelerator': ['accelerator'], 'Incubator': ['incubator'],
            'Knowledge Transfer Partnership': ['knowledge transfer partnership', 'ktp'],
            'Grant': ['grant', 'funding'], 'Competition': ['competition', 'challenge'],
            'Fellowship': ['fellowship']
        }
        full_text_lower = full_text.lower()
        for prog_type, indicators in programme_indicators.items():
            if any(indicator in full_text_lower for indicator in indicators):
                details['programme_type'] = prog_type
                break

        # Sectors (should always be from full_text)
        sector_keywords = ['agri-tech', 'health', 'manufacturing', 'digital', 'energy', 'transport',
                           'aerospace', 'automotive', 'biotechnology', 'fintech', 'quantum', 'space',
                           'creative', 'circular economy', 'net zero']
        detected_sectors = [sector for sector in sector_keywords if sector in full_text_lower]
        details['sectors'] = detected_sectors[:5]

        return details

    def scrape_opportunity_details(self, opportunity):
        print(f"Fetching details for: {opportunity['title'][:50]}...")
        response = self.get_page(opportunity['url'])
        if not response:
            return opportunity
        soup = BeautifulSoup(response.content, 'html.parser')
        structured_details = self.extract_structured_details(soup)
        opportunity.update(structured_details)
        opportunity['scraped_at'] = datetime.now().isoformat()
        time.sleep(0.5)
        return opportunity

    def scrape_all(self, include_details=True, max_opportunities=None):
        opportunities = self.scrape_opportunities_list()
        if max_opportunities:
            opportunities = opportunities[:max_opportunities]
            print(f"Limited to first {max_opportunities} opportunities for testing")
        if include_details and opportunities:
            print(f"\nFetching detailed information for {len(opportunities)} opportunities...")
            for i, opp in enumerate(opportunities, 1):
                print(f"Progress: {i}/{len(opportunities)}")
                opportunities[i-1] = self.scrape_opportunity_details(opp)
        return opportunities

    def save_to_csv(self, opportunities, filename=None):
        if not opportunities:
            print("No opportunities to save")
            return None
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"innovate_uk_opportunities_{timestamp}.csv"
        opportunities_copy = []
        for opp in opportunities:
            opp_copy = {}
            for key, value in opp.items():
                if isinstance(value, datetime):
                    opp_copy[key] = value.isoformat()
                elif isinstance(value, list):
                    opp_copy[key] = '; '.join(str(item) for item in value) if value else ''
                else:
                    opp_copy[key] = value
            opportunities_copy.append(opp_copy)
        df = pd.DataFrame(opportunities_copy)
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"Data saved to {filename}")
        return filename

    def save_to_json(self, opportunities, filename=None):
        if not opportunities:
            print("No opportunities to save")
            return None
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"innovate_uk_opportunities_{timestamp}.json"
        opportunities_copy = []
        for opp in opportunities:
            opp_copy = {}
            for key, value in opp.items():
                if isinstance(value, datetime):
                    opp_copy[key] = value.isoformat()
                else:
                    opp_copy[key] = value
            opportunities_copy.append(opp_copy)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(opportunities_copy, f, indent=2, ensure_ascii=False)
        print(f"Data saved to {filename}")
        return filename

    def print_sample_data(self, opportunities, detailed=True):
        if not opportunities:
            print("No opportunities found")
            return
        print(f"\n=== Sample Data (showing 1 of {len(opportunities)} opportunities) ===")
        sample = opportunities[0]
        basic_fields = ['title', 'url', 'opens_date', 'closes_date', 'brief_description', 'funding_amount', 'currency']
        detail_fields = ['organisation', 'registration_opens', 'registration_closes',
                         'detailed_funding_amount', 'detailed_currency', 'programme_type', 'award_info',
                         'background', 'challenge_description', 'requirements', 'assessment_criteria',
                         'timeline', 'application_process', 'ip_arrangements', 'sectors', 'eligibility', 'contact_info']
        print("\n--- Basic Information ---")
        for field in basic_fields:
            if field in sample and sample[field]:
                value = sample[field]
                if isinstance(value, datetime):
                    value = value.strftime('%Y-%m-%d %H:%M')
                print(f"{field}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        if detailed:
            print("\n--- Detailed Information ---")
            for field in detail_fields:
                if field in sample and sample[field]:
                    value = sample[field]
                    if isinstance(value, datetime):
                        value = value.strftime('%Y-%m-%d %H:%M')
                    elif isinstance(value, list):
                        value = '; '.join(str(item) for item in value)
                    print(f"{field}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")

if __name__ == "__main__":
    scraper = InnovateUKScraper()
    
    print("=== Quick Scrape (List Only) ===")
    opportunities_basic = scraper.scrape_all(include_details=False) # Full scrape without limits
    
    if opportunities_basic:
        scraper.save_to_csv(opportunities_basic, "iuk_opportunities_basic.csv")
        scraper.print_sample_data(opportunities_basic, detailed=False)
        
        print("\n=== Full Scrape with Details ===")
        # Full scrape of all opportunities with detailed information
        opportunities_detailed = scraper.scrape_all(include_details=True) 
        
        if opportunities_detailed:
            scraper.save_to_csv(opportunities_detailed, "iuk_opportunities_detailed.csv")
            scraper.save_to_json(opportunities_detailed, "iuk_opportunities_detailed.json")
            
            print(f"\nCompleted! Found {len(opportunities_detailed)} opportunities")
            scraper.print_sample_data(opportunities_detailed, detailed=True)
    else:
        print("No opportunities found. Please check the website structure or URL.")
