#!/usr/bin/env python3
"""
Test script to verify the enhanced funding opportunities system
"""

import json
import os
from datetime import datetime
from funding_data_manager import FundingDataManager, FundingOpportunity

def test_currency_categorization():
    """Test the improved currency handling"""
    print("🧪 Testing currency categorization...")
    
    manager = FundingDataManager()
    
    test_cases = [
        ("£50,000", "Under £100k"),
        ("$225", "Under £100k"),  # This was the problematic case
        ("€500,000", "£100k - £1M"),
        ("£2.5m", "£1M - £10M"),
        ("$15 million", "Over £10M"),
        ("See details", "No amount specified"),
    ]
    
    for amount, expected in test_cases:
        result = manager.categorize_funding_amount(amount)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {amount} -> {result} (expected: {expected})")
    
    print()

def test_data_conversion():
    """Test data conversion between formats"""
    print("🔄 Testing data conversion...")
    
    manager = FundingDataManager()
    
    # Test IUK data conversion
    iuk_sample = {
        'title': 'Test Innovation Challenge',
        'url': 'https://example.com/test',
        'funding_amount': '$50,000',
        'currency': '$',
        'organisation': 'Test Org',
        'brief_description': 'A test opportunity for sustainable innovation',
        'sectors': ['energy', 'sustainability']
    }
    
    unified_iuk = manager.convert_iuk_opportunity(iuk_sample)
    print(f"  ✅ IUK conversion: {unified_iuk.title}")
    print(f"     Source: {unified_iuk.source}")
    print(f"     Sustainable: {unified_iuk.is_sustainable}")
    print(f"     Funding Category: {unified_iuk.funding_category}")
    
    # Test tender data conversion
    tender_sample = {
        'title': 'Net Zero Carbon Reduction',
        'url': 'https://example.com/tender',
        'value_amount': 250000,
        'value_currency': 'GBP',
        'buyer_name': 'Test Department',
        'description': 'Climate change adaptation project',
        'matched_keywords': ['net zero', 'climate']
    }
    
    unified_tender = manager.convert_tender_opportunity(tender_sample)
    print(f"  ✅ Tender conversion: {unified_tender.title}")
    print(f"     Source: {unified_tender.source}")
    print(f"     Sustainable: {unified_tender.is_sustainable}")
    print(f"     Funding Category: {unified_tender.funding_category}")
    
    print()

def test_data_persistence():
    """Test saving and loading data"""
    print("💾 Testing data persistence...")
    
    manager = FundingDataManager()
    
    # Create test opportunities
    test_opportunities = [
        FundingOpportunity(
            id="test_1",
            title="Test Opportunity 1",
            url="https://example.com/1",
            source="innovate_uk",
            funding_amount="£100,000",
            funding_category="Under £100k",
            is_sustainable=True
        ),
        FundingOpportunity(
            id="test_2", 
            title="Test Opportunity 2",
            url="https://example.com/2",
            source="net_zero_tenders",
            funding_amount="GBP 500,000",
            funding_category="£100k - £1M",
            is_sustainable=True
        )
    ]
    
    # Save data
    json_file, csv_file = manager.save_opportunities(test_opportunities, "test_data")
    print(f"  ✅ Saved to: {json_file}")
    print(f"  ✅ Saved to: {csv_file}")
    
    # Load data back
    loaded_opportunities = manager.load_opportunities(json_file)
    print(f"  ✅ Loaded {len(loaded_opportunities)} opportunities")
    
    # Verify data integrity
    if len(loaded_opportunities) == len(test_opportunities):
        print("  ✅ Data integrity verified")
    else:
        print("  ❌ Data integrity check failed")
    
    # Clean up test files
    try:
        os.remove(json_file)
        os.remove(csv_file)
        print("  ✅ Test files cleaned up")
    except:
        print("  ⚠️  Could not clean up test files")
    
    print()

def test_sustainability_detection():
    """Test sustainability keyword detection"""
    print("🌱 Testing sustainability detection...")
    
    manager = FundingDataManager()
    
    test_cases = [
        ("Clean Energy Innovation Challenge", True),
        ("Net Zero Carbon Reduction Programme", True),
        ("Digital Health Innovation", False),
        ("Sustainable transport solutions", True),
        ("AI for climate change adaptation", True),
        ("General business innovation", False),
    ]
    
    for text, expected in test_cases:
        result = manager.check_sustainability(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text}' -> {result} (expected: {expected})")
    
    print()

def test_system_integration():
    """Test overall system integration"""
    print("🔗 Testing system integration...")
    
    # Check if all required files exist
    required_files = [
        'enhanced_funding_dashboard.html',
        'api_server.py',
        'funding_data_manager.py',
        'iuk_scraper.py',
        'net_zero_tender_finder.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (missing)")
            missing_files.append(file)
    
    if not missing_files:
        print("  ✅ All required files present")
    else:
        print(f"  ❌ Missing files: {missing_files}")
    
    print()

def main():
    """Run all tests"""
    print("🧪 Enhanced Funding Opportunities System Test")
    print("=" * 50)
    print()
    
    test_currency_categorization()
    test_data_conversion()
    test_data_persistence()
    test_sustainability_detection()
    test_system_integration()
    
    print("🎉 Testing completed!")
    print()
    print("To start the system:")
    print("  python start_dashboard.py")
    print()
    print("Or manually:")
    print("  python api_server.py")
    print("  Then open: http://localhost:5000")

if __name__ == "__main__":
    main()
