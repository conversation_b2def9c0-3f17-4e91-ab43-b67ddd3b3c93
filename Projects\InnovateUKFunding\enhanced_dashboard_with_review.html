<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Funding Opportunities Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .controls {
            padding: 25px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .control-section {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .control-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .control-section h3 i {
            margin-right: 10px;
            color: #3498db;
        }

        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #138496);
        }

        .status-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .status-card h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .status-card .value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
        }

        .config-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .config-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .config-group input, .config-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        .review-section {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .review-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #f39c12;
        }

        .review-item.relevant {
            border-left-color: #27ae60;
        }

        .review-item.not-relevant {
            border-left-color: #e74c3c;
        }

        .review-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .review-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
            flex-grow: 1;
        }

        .review-score {
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            margin-left: 10px;
        }

        .review-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .content {
            padding: 30px;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .filter-group input, .filter-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th {
            background: #2c3e50;
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid #eee;
            vertical-align: top;
        }

        tr:hover {
            background: #f8f9fa;
        }

        .source-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .source-iuk {
            background: #e3f2fd;
            color: #1976d2;
        }

        .source-tender {
            background: #e8f5e8;
            color: #388e3c;
        }

        .funding-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            background: #fff3e0;
            color: #f57c00;
        }

        .sustainable-badge {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.75rem;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .log-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-seedling"></i> Enhanced Funding Opportunities Dashboard</h1>
            <p>Smart Scraping • Human Review • LLM Filtering • Deduplication</p>
        </div>

        <div class="controls">
            <!-- Smart Scraping Controls -->
            <div class="control-section">
                <h3><i class="fas fa-robot"></i> Smart Scraping Controls</h3>
                <div class="config-options">
                    <div class="config-group">
                        <label>IUK Scraper Settings</label>
                        <div class="checkbox-group">
                            <input type="checkbox" id="force-iuk-rescrape">
                            <label for="force-iuk-rescrape">Force re-scrape (ignore date check)</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="include-details" checked>
                            <label for="include-details">Include detailed information</label>
                        </div>
                    </div>
                    <div class="config-group">
                        <label>Net Zero Finder Settings</label>
                        <label for="days-back">Days to search back:</label>
                        <input type="number" id="days-back" value="14" min="1" max="90">
                        <label for="min-relevance">Minimum relevance score:</label>
                        <input type="number" id="min-relevance" value="30" min="0" max="100">
                    </div>
                    <div class="config-group">
                        <label>Review Settings</label>
                        <div class="checkbox-group">
                            <input type="checkbox" id="enable-human-review">
                            <label for="enable-human-review">Enable human review</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="enable-llm-review">
                            <label for="enable-llm-review">Enable LLM review</label>
                        </div>
                        <label for="auto-accept-threshold">Auto-accept threshold:</label>
                        <input type="number" id="auto-accept-threshold" value="70" min="0" max="100">
                    </div>
                </div>

                <div class="control-grid">
                    <button class="btn btn-success" onclick="runSmartScrapers()">
                        <i class="fas fa-brain"></i> Run Smart Scrapers
                    </button>
                    <button class="btn btn-info" onclick="runIUKOnly()">
                        <i class="fas fa-building"></i> IUK Only (Smart)
                    </button>
                    <button class="btn btn-info" onclick="runNetZeroOnly()">
                        <i class="fas fa-leaf"></i> Net Zero Only (LLM)
                    </button>
                    <button class="btn btn-warning" onclick="showReviewQueue()">
                        <i class="fas fa-clipboard-check"></i> Review Queue
                    </button>
                </div>
            </div>

            <!-- Traditional Controls -->
            <div class="control-section">
                <h3><i class="fas fa-cogs"></i> Traditional Scraping (Legacy)</h3>
                <div class="control-grid">
                    <button class="btn" onclick="runAllScrapers()">
                        <i class="fas fa-rocket"></i> Run All Scrapers
                    </button>
                    <button class="btn" onclick="runIUKScraper()">
                        <i class="fas fa-building"></i> Innovate UK Only
                    </button>
                    <button class="btn" onclick="runTenderScraper()">
                        <i class="fas fa-search"></i> Net Zero Tenders Only
                    </button>
                    <button class="btn btn-info" onclick="exportToCSV()">
                        <i class="fas fa-download"></i> Export to CSV
                    </button>
                </div>
            </div>

            <!-- Data Management -->
            <div class="control-section">
                <h3><i class="fas fa-database"></i> Data Management</h3>
                <div class="control-grid">
                    <button class="btn btn-info" onclick="viewDataStats()">
                        <i class="fas fa-chart-bar"></i> View Statistics
                    </button>
                    <button class="btn btn-warning" onclick="cleanTitles()">
                        <i class="fas fa-broom"></i> Clean All Titles
                    </button>
                    <button class="btn btn-danger" onclick="resetMasterData()">
                        <i class="fas fa-trash"></i> Reset Master Data
                    </button>
                    <button class="btn" onclick="refreshData()">
                        <i class="fas fa-sync"></i> Refresh Dashboard
                    </button>
                </div>
            </div>

            <!-- Status Display -->
            <div class="status-section">
                <div class="status-card">
                    <h4>Total Opportunities</h4>
                    <div class="value" id="total-count">Loading...</div>
                </div>
                <div class="status-card">
                    <h4>New This Run</h4>
                    <div class="value" id="new-count">-</div>
                </div>
                <div class="status-card">
                    <h4>Sustainability Related</h4>
                    <div class="value" id="sustainable-count">Loading...</div>
                </div>
                <div class="status-card">
                    <h4>Last Updated</h4>
                    <div class="value" id="last-updated">Loading...</div>
                </div>
            </div>
        </div>

        <!-- Review Section (initially hidden) -->
        <div id="review-section" class="review-section hidden">
            <h3><i class="fas fa-clipboard-check"></i> Human Review Queue</h3>
            <div id="review-items">
                <!-- Review items will be populated here -->
            </div>
        </div>

        <div class="content">
            <div class="filters">
                <div class="filter-group">
                    <label for="search-filter">Search:</label>
                    <input type="text" id="search-filter" placeholder="Search opportunities...">
                </div>
                <div class="filter-group">
                    <label for="source-filter">Source:</label>
                    <select id="source-filter">
                        <option value="">All Sources</option>
                        <option value="innovate_uk">Innovate UK</option>
                        <option value="net_zero_tenders">Net Zero Tenders</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="funding-filter">Funding Level:</label>
                    <select id="funding-filter">
                        <option value="">All Levels</option>
                        <option value="Under £100k">Under £100k</option>
                        <option value="£100k - £1M">£100k - £1M</option>
                        <option value="£1M - £10M">£1M - £10M</option>
                        <option value="Over £10M">Over £10M</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sustainability-filter">Sustainability:</label>
                    <select id="sustainability-filter">
                        <option value="">All</option>
                        <option value="true">Sustainability Related</option>
                        <option value="false">General Funding</option>
                    </select>
                </div>
            </div>

            <div class="table-container">
                <table id="opportunities-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Source</th>
                            <th>Organisation</th>
                            <th>Funding</th>
                            <th>Closes</th>
                            <th>Sustainable</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="opportunities-tbody">
                        <tr>
                            <td colspan="7" class="loading">
                                <div class="spinner"></div>
                                Loading opportunities...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal for opportunity details -->
    <div id="opportunityModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modal-content">
                <!-- Modal content will be populated here -->
            </div>
        </div>
    </div>

    <!-- Log output modal -->
    <div id="logModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Scraper Output</h3>
            <div id="log-content" class="log-output"></div>
        </div>
    </div>

    <script>
        let allOpportunities = [];
        let filteredOpportunities = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            loadStatus();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Filter event listeners
            document.getElementById('search-filter').addEventListener('input', applyFilters);
            document.getElementById('source-filter').addEventListener('change', applyFilters);
            document.getElementById('funding-filter').addEventListener('change', applyFilters);
            document.getElementById('sustainability-filter').addEventListener('change', applyFilters);

            // Modal event listeners
            document.querySelectorAll('.close').forEach(closeBtn => {
                closeBtn.addEventListener('click', function() {
                    this.closest('.modal').style.display = 'none';
                });
            });

            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });
        }

        // Smart scraping functions
        async function runSmartScrapers() {
            const config = {
                force_iuk_rescrape: document.getElementById('force-iuk-rescrape').checked,
                include_details: document.getElementById('include-details').checked,
                days_back: parseInt(document.getElementById('days-back').value),
                min_relevance_score: parseFloat(document.getElementById('min-relevance').value),
                enable_human_review: document.getElementById('enable-human-review').checked,
                enable_llm_review: document.getElementById('enable-llm-review').checked,
                auto_accept_threshold: parseFloat(document.getElementById('auto-accept-threshold').value)
            };

            showLoadingModal('Running smart scrapers...');

            try {
                const response = await fetch('/api/run-smart-scrapers', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(config)
                });

                const result = await response.json();
                
                if (result.success) {
                    hideLoadingModal();
                    
                    if (result.requires_review) {
                        showReviewQueue(result.review_items);
                    } else {
                        await loadData();
                        await loadStatus();
                        showSuccessMessage(`Smart scraping completed! Found ${result.new_count} new opportunities.`);
                    }
                } else {
                    hideLoadingModal();
                    showErrorMessage(result.message || 'Smart scraping failed');
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error running smart scrapers: ' + error.message);
            }
        }

        async function runIUKOnly() {
            const forceRescrape = document.getElementById('force-iuk-rescrape').checked;
            showLoadingModal('Running IUK scraper...');

            try {
                const response = await fetch('/api/run-iuk-smart', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({force_rescrape: forceRescrape})
                });

                const result = await response.json();
                hideLoadingModal();

                if (result.success) {
                    await loadData();
                    await loadStatus();
                    showSuccessMessage(result.message);
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }

        async function runNetZeroOnly() {
            const config = {
                days_back: parseInt(document.getElementById('days-back').value),
                min_relevance_score: parseFloat(document.getElementById('min-relevance').value),
                enable_llm: document.getElementById('enable-llm-review').checked
            };

            showLoadingModal('Running Net Zero scraper with LLM filtering...');

            try {
                const response = await fetch('/api/run-netzero-smart', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(config)
                });

                const result = await response.json();
                hideLoadingModal();

                if (result.success) {
                    await loadData();
                    await loadStatus();
                    showSuccessMessage(result.message);
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }

        function showReviewQueue(reviewItems = null) {
            const reviewSection = document.getElementById('review-section');
            const reviewItemsContainer = document.getElementById('review-items');

            if (reviewItems) {
                // Populate review items
                reviewItemsContainer.innerHTML = reviewItems.map(item => `
                    <div class="review-item" data-id="${item.id}">
                        <div class="review-header">
                            <div class="review-title">${item.title}</div>
                            <div class="review-score">Score: ${item.score.toFixed(1)}</div>
                        </div>
                        <p><strong>Buyer:</strong> ${item.buyer}</p>
                        <p><strong>Keywords:</strong> ${item.keywords.join(', ')}</p>
                        <p><strong>Description:</strong> ${item.description.substring(0, 200)}...</p>
                        <div class="review-actions">
                            <button class="btn btn-success" onclick="approveOpportunity('${item.id}')">
                                <i class="fas fa-check"></i> Relevant
                            </button>
                            <button class="btn btn-danger" onclick="rejectOpportunity('${item.id}')">
                                <i class="fas fa-times"></i> Not Relevant
                            </button>
                            <button class="btn" onclick="skipOpportunity('${item.id}')">
                                <i class="fas fa-forward"></i> Skip
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            reviewSection.classList.remove('hidden');
        }

        async function approveOpportunity(opportunityId) {
            await reviewOpportunity(opportunityId, 'relevant');
        }

        async function rejectOpportunity(opportunityId) {
            await reviewOpportunity(opportunityId, 'not_relevant');
        }

        async function skipOpportunity(opportunityId) {
            await reviewOpportunity(opportunityId, 'skip');
        }

        async function reviewOpportunity(opportunityId, decision) {
            try {
                const response = await fetch('/api/review-opportunity', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        opportunity_id: opportunityId,
                        decision: decision
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // Remove the reviewed item from the UI
                    const reviewItem = document.querySelector(`[data-id="${opportunityId}"]`);
                    if (reviewItem) {
                        reviewItem.classList.add(decision === 'relevant' ? 'relevant' : 'not-relevant');
                        setTimeout(() => reviewItem.remove(), 1000);
                    }

                    // Check if all items are reviewed
                    const remainingItems = document.querySelectorAll('.review-item');
                    if (remainingItems.length === 0) {
                        document.getElementById('review-section').classList.add('hidden');
                        await loadData();
                        await loadStatus();
                        showSuccessMessage('Review completed!');
                    }
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                showErrorMessage('Error submitting review: ' + error.message);
            }
        }

        // Traditional scraping functions (unchanged)
        async function runAllScrapers() {
            showLoadingModal('Running all scrapers...');
            try {
                const response = await fetch('/api/run-all-scrapers', {method: 'POST'});
                const result = await response.json();
                hideLoadingModal();
                if (result.success) {
                    await loadData();
                    await loadStatus();
                    showSuccessMessage('All scrapers completed successfully!');
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }

        // Data management functions
        async function viewDataStats() {
            try {
                const response = await fetch('/api/data-stats');
                const stats = await response.json();
                
                showModal('Data Statistics', `
                    <div class="status-section">
                        <div class="status-card">
                            <h4>Total Opportunities</h4>
                            <div class="value">${stats.total_opportunities}</div>
                        </div>
                        <div class="status-card">
                            <h4>By Source</h4>
                            <div>IUK: ${stats.iuk_count}</div>
                            <div>Tenders: ${stats.tender_count}</div>
                        </div>
                        <div class="status-card">
                            <h4>Sustainability Related</h4>
                            <div class="value">${stats.sustainable_count}</div>
                        </div>
                        <div class="status-card">
                            <h4>Last Updated</h4>
                            <div>${new Date(stats.last_updated).toLocaleString()}</div>
                        </div>
                    </div>
                `);
            } catch (error) {
                showErrorMessage('Error loading statistics: ' + error.message);
            }
        }

        async function cleanTitles() {
            if (!confirm('This will clean all titles in the master data. Continue?')) return;
            
            showLoadingModal('Cleaning titles...');
            try {
                const response = await fetch('/api/clean-titles', {method: 'POST'});
                const result = await response.json();
                hideLoadingModal();
                
                if (result.success) {
                    await loadData();
                    showSuccessMessage(`Cleaned ${result.cleaned_count} titles`);
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }

        async function resetMasterData() {
            if (!confirm('This will reset the master data file (backup will be created). Continue?')) return;
            
            showLoadingModal('Resetting master data...');
            try {
                const response = await fetch('/api/reset-master-data', {method: 'POST'});
                const result = await response.json();
                hideLoadingModal();
                
                if (result.success) {
                    await loadData();
                    await loadStatus();
                    showSuccessMessage('Master data reset successfully');
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }

        // Utility functions
        function showLoadingModal(message) {
            showModal('Loading', `
                <div class="loading">
                    <div class="spinner"></div>
                    ${message}
                </div>
            `);
        }

        function hideLoadingModal() {
            document.getElementById('opportunityModal').style.display = 'none';
        }

        function showModal(title, content) {
            document.getElementById('modal-content').innerHTML = `<h3>${title}</h3>${content}`;
            document.getElementById('opportunityModal').style.display = 'block';
        }

        function showSuccessMessage(message) {
            showModal('Success', `<p style="color: green;"><i class="fas fa-check-circle"></i> ${message}</p>`);
        }

        function showErrorMessage(message) {
            showModal('Error', `<p style="color: red;"><i class="fas fa-exclamation-triangle"></i> ${message}</p>`);
        }

        // Load data and status (existing functions remain the same)
        async function loadData() {
            try {
                const response = await fetch('/api/get-latest-data');
                const data = await response.json();
                allOpportunities = data.opportunities || [];
                applyFilters();
            } catch (error) {
                console.error('Error loading data:', error);
                document.getElementById('opportunities-tbody').innerHTML = 
                    '<tr><td colspan="7" style="text-align: center; color: red;">Error loading data</td></tr>';
            }
        }

        async function loadStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                document.getElementById('total-count').textContent = status.total_opportunities || 0;
                document.getElementById('new-count').textContent = status.new_opportunities_this_run || 0;
                document.getElementById('sustainable-count').textContent = status.sustainable_count || 0;
                document.getElementById('last-updated').textContent = 
                    status.last_updated ? new Date(status.last_updated).toLocaleString() : 'Never';
            } catch (error) {
                console.error('Error loading status:', error);
            }
        }

        function applyFilters() {
            const searchTerm = document.getElementById('search-filter').value.toLowerCase();
            const sourceFilter = document.getElementById('source-filter').value;
            const fundingFilter = document.getElementById('funding-filter').value;
            const sustainabilityFilter = document.getElementById('sustainability-filter').value;

            filteredOpportunities = allOpportunities.filter(opp => {
                const matchesSearch = !searchTerm || 
                    opp.title.toLowerCase().includes(searchTerm) ||
                    (opp.organisation && opp.organisation.toLowerCase().includes(searchTerm));
                
                const matchesSource = !sourceFilter || opp.source === sourceFilter;
                const matchesFunding = !fundingFilter || opp.funding_category === fundingFilter;
                const matchesSustainability = !sustainabilityFilter || 
                    opp.is_sustainable.toString() === sustainabilityFilter;

                return matchesSearch && matchesSource && matchesFunding && matchesSustainability;
            });

            renderTable();
        }

        function renderTable() {
            const tbody = document.getElementById('opportunities-tbody');
            
            if (filteredOpportunities.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">No opportunities found matching current filters</td></tr>';
                return;
            }

            tbody.innerHTML = filteredOpportunities.map(opp => `
                <tr>
                    <td>
                        <strong>${opp.title}</strong>
                        <br><small style="color: #666;">${opp.brief_description || ''}</small>
                    </td>
                    <td>
                        <span class="source-badge source-${opp.source === 'innovate_uk' ? 'iuk' : 'tender'}">
                            ${opp.source === 'innovate_uk' ? 'Innovate UK' : 'Net Zero Tenders'}
                        </span>
                    </td>
                    <td>${opp.organisation || opp.buyer_name || 'Not specified'}</td>
                    <td>
                        ${opp.funding_category ? `<span class="funding-badge">${opp.funding_category}</span>` : 'Not specified'}
                        ${opp.funding_amount ? `<br><small>${opp.funding_amount}</small>` : ''}
                    </td>
                    <td>${opp.closes_date ? new Date(opp.closes_date).toLocaleDateString() : 'Not specified'}</td>
                    <td>
                        ${opp.is_sustainable ? '<span class="sustainable-badge">✓ Sustainable</span>' : ''}
                    </td>
                    <td>
                        <button class="btn" style="font-size: 0.8rem; padding: 5px 10px;" 
                                onclick="viewOpportunity('${opp.id}')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <br>
                        <a href="${opp.url}" target="_blank" style="font-size: 0.8rem;">
                            <i class="fas fa-external-link-alt"></i> Open
                        </a>
                    </td>
                </tr>
            `).join('');
        }

        async function viewOpportunity(id) {
            const opportunity = allOpportunities.find(opp => opp.id === id);
            if (!opportunity) return;

            const content = `
                <h3>${opportunity.title}</h3>
                <p><strong>Source:</strong> ${opportunity.source === 'innovate_uk' ? 'Innovate UK' : 'Net Zero Tenders'}</p>
                <p><strong>Organisation:</strong> ${opportunity.organisation || opportunity.buyer_name || 'Not specified'}</p>
                <p><strong>Funding:</strong> ${opportunity.funding_amount || 'Not specified'}</p>
                <p><strong>Opens:</strong> ${opportunity.opens_date ? new Date(opportunity.opens_date).toLocaleDateString() : 'Not specified'}</p>
                <p><strong>Closes:</strong> ${opportunity.closes_date ? new Date(opportunity.closes_date).toLocaleDateString() : 'Not specified'}</p>
                <p><strong>Sustainable:</strong> ${opportunity.is_sustainable ? 'Yes' : 'No'}</p>
                ${opportunity.description ? `<p><strong>Description:</strong> ${opportunity.description}</p>` : ''}
                <p><strong>URL:</strong> <a href="${opportunity.url}" target="_blank">${opportunity.url}</a></p>
            `;

            showModal('Opportunity Details', content);
        }

        async function exportToCSV() {
            try {
                const response = await fetch('/api/export-csv', {method: 'POST'});
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `funding_opportunities_${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showSuccessMessage('CSV file downloaded successfully!');
            } catch (error) {
                showErrorMessage('Error exporting CSV: ' + error.message);
            }
        }

        async function refreshData() {
            await loadData();
            await loadStatus();
            showSuccessMessage('Data refreshed successfully!');
        }

        // Additional scraper functions (unchanged)
        async function runIUKScraper() {
            showLoadingModal('Running Innovate UK scraper...');
            try {
                const response = await fetch('/api/run-iuk-scraper', {method: 'POST'});
                const result = await response.json();
                hideLoadingModal();
                if (result.success) {
                    await loadData();
                    await loadStatus();
                    showSuccessMessage('IUK scraper completed successfully!');
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }

        async function runTenderScraper() {
            showLoadingModal('Running Net Zero tender scraper...');
            try {
                const response = await fetch('/api/run-tender-scraper', {method: 'POST'});
                const result = await response.json();
                hideLoadingModal();
                if (result.success) {
                    await loadData();
                    await loadStatus();
                    showSuccessMessage('Tender scraper completed successfully!');
                } else {
                    showErrorMessage(result.message);
                }
            } catch (error) {
                hideLoadingModal();
                showErrorMessage('Error: ' + error.message);
            }
        }
    </script>
</body>
</html> 