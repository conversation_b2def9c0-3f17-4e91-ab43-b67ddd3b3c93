#!/usr/bin/env python3
"""
Startup script for the Enhanced Funding Opportunities Dashboard
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import requests
        import bs4
        import pandas
        import flask
        import flask_cors
        print("✅ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please install dependencies with: pip install -r requirements.txt")
        return False

def start_server():
    """Start the Flask API server"""
    print("🚀 Starting the Enhanced Funding Opportunities Dashboard...")
    print("=" * 60)
    
    if not check_dependencies():
        return False
    
    # Check if we're in the right directory
    if not Path("api_server.py").exists():
        print("❌ api_server.py not found. Please run this script from the project directory.")
        return False
    
    print("📊 Dashboard will be available at: http://localhost:5000")
    print("🔧 API endpoints will be available at: http://localhost:5000/api/")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 60)
    
    try:
        # Start the Flask server
        subprocess.run([sys.executable, "api_server.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
        return False
    
    return True

def open_browser():
    """Open the dashboard in the default browser"""
    time.sleep(2)  # Give the server time to start
    webbrowser.open("http://localhost:5000")

if __name__ == "__main__":
    print("🌱 Enhanced Funding Opportunities Dashboard")
    print("Integrated scraping and analysis of funding opportunities")
    print()
    
    # Check for command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--no-browser":
        start_server()
    else:
        # Start server and open browser
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        start_server()
