#!/usr/bin/env python3
"""
LLM-Based Relevance Checker for Net Zero Opportunities
Uses AI to automatically determine if opportunities are genuinely related to Net Zero/Sustainability
"""

import json
import openai
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from net_zero_tender_finder import TenderOpportunity
import re

@dataclass
class RelevanceAssessment:
    """Assessment of opportunity relevance"""
    is_relevant: bool
    confidence_score: float  # 0-100
    reasoning: str
    category: str  # e.g., "Energy", "Transport", "Buildings", "Waste", etc.
    false_positive_indicators: List[str]

class LLMRelevanceChecker:
    """LLM-based checker for Net Zero opportunity relevance"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-3.5-turbo"):
        """Initialize the LLM checker
        
        Args:
            api_key: OpenAI API key (if None, will try environment variable)
            model: Model to use for assessment
        """
        self.model = model
        
        if api_key:
            openai.api_key = api_key
        
        self.system_prompt = """You are an expert in UK Net Zero policy and sustainability procurement. Your job is to assess whether procurement opportunities are genuinely related to Net Zero, climate action, environmental sustainability, or the clean energy transition.

You should identify opportunities that are:
✅ RELEVANT:
- Renewable energy projects (solar, wind, hydro, etc.)
- Energy efficiency improvements
- Electric vehicle infrastructure
- Building retrofits and heat pumps
- Carbon reduction initiatives
- Waste reduction and circular economy
- Sustainable transport
- Environmental monitoring and improvement
- Climate adaptation measures
- Green infrastructure and nature-based solutions

❌ NOT RELEVANT (common false positives):
- General office services (even if they mention "green office environment")
- Healthcare services (even if they mention "healthy environment")
- IT services and software (unless specifically for environmental monitoring)
- General construction (unless specifically for energy efficiency)
- Social services, education, HR services
- Generic maintenance and cleaning
- Financial and legal services
- Research that's not climate/environment focused

Assess each opportunity and provide:
1. Is it relevant? (true/false)
2. Confidence score (0-100)
3. Brief reasoning (2-3 sentences)
4. Category if relevant (Energy, Transport, Buildings, Waste, Nature, etc.)
5. Any false positive indicators found

Be strict - only mark as relevant if there's clear evidence of Net Zero/sustainability focus."""

    def assess_opportunity(self, opportunity: TenderOpportunity) -> RelevanceAssessment:
        """Assess a single opportunity using LLM"""
        
        # Prepare the content for assessment
        content = f"""
TITLE: {opportunity.title}

BUYER: {opportunity.buyer_name}

VALUE: {opportunity.value_currency} {opportunity.value_amount:,.2f if opportunity.value_amount else 'Not specified'}

MATCHED KEYWORDS: {', '.join(opportunity.matched_keywords)}

DESCRIPTION: {opportunity.description[:1000]}

CPV CODES: {' | '.join(opportunity.cpv_codes) if opportunity.cpv_codes else 'None'}

URL: {opportunity.url}
"""

        user_prompt = f"""Please assess this UK procurement opportunity for Net Zero/sustainability relevance:

{content}

Respond in this exact JSON format:
{{
    "is_relevant": true/false,
    "confidence_score": 0-100,
    "reasoning": "Brief explanation of your assessment",
    "category": "Energy/Transport/Buildings/Waste/Nature/Other" or null if not relevant,
    "false_positive_indicators": ["list", "of", "reasons", "if", "not", "relevant"]
}}"""

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.1  # Low temperature for consistent assessment
            )
            
            # Parse the JSON response
            response_text = response.choices[0].message.content.strip()
            
            # Extract JSON from response (in case there's extra text)
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                result = json.loads(json_match.group())
                
                return RelevanceAssessment(
                    is_relevant=result.get('is_relevant', False),
                    confidence_score=float(result.get('confidence_score', 0)),
                    reasoning=result.get('reasoning', ''),
                    category=result.get('category'),
                    false_positive_indicators=result.get('false_positive_indicators', [])
                )
            else:
                # Fallback if JSON parsing fails
                return RelevanceAssessment(
                    is_relevant=False,
                    confidence_score=0,
                    reasoning="Failed to parse LLM response",
                    category=None,
                    false_positive_indicators=["LLM parsing error"]
                )
                
        except Exception as e:
            print(f"Error in LLM assessment: {e}")
            return RelevanceAssessment(
                is_relevant=False,
                confidence_score=0,
                reasoning=f"LLM error: {str(e)}",
                category=None,
                false_positive_indicators=["LLM API error"]
            )
    
    def assess_batch(self, opportunities: List[TenderOpportunity], 
                    confidence_threshold: float = 70.0) -> Tuple[List[TenderOpportunity], List[RelevanceAssessment]]:
        """Assess a batch of opportunities
        
        Args:
            opportunities: List of opportunities to assess
            confidence_threshold: Minimum confidence score to include opportunity
            
        Returns:
            Tuple of (relevant_opportunities, all_assessments)
        """
        relevant_opportunities = []
        all_assessments = []
        
        print(f"🤖 LLM RELEVANCE ASSESSMENT")
        print(f"Assessing {len(opportunities)} opportunities...")
        print(f"Confidence threshold: {confidence_threshold}%")
        
        for i, opportunity in enumerate(opportunities, 1):
            print(f"Progress: {i}/{len(opportunities)} - {opportunity.title[:50]}...")
            
            assessment = self.assess_opportunity(opportunity)
            all_assessments.append(assessment)
            
            # Include if relevant and meets confidence threshold
            if assessment.is_relevant and assessment.confidence_score >= confidence_threshold:
                relevant_opportunities.append(opportunity)
                print(f"  ✅ RELEVANT ({assessment.confidence_score:.0f}%): {assessment.category}")
            else:
                print(f"  ❌ Not relevant ({assessment.confidence_score:.0f}%)")
                if assessment.false_positive_indicators:
                    print(f"     Reasons: {', '.join(assessment.false_positive_indicators[:2])}")
        
        print(f"\n📊 ASSESSMENT COMPLETE")
        print(f"Relevant opportunities: {len(relevant_opportunities)}/{len(opportunities)}")
        
        return relevant_opportunities, all_assessments
    
    def save_assessment_report(self, opportunities: List[TenderOpportunity], 
                             assessments: List[RelevanceAssessment], 
                             filename: str = None) -> str:
        """Save detailed assessment report"""
        if not filename:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"llm_assessment_report_{timestamp}.json"
        
        report_data = []
        for opp, assessment in zip(opportunities, assessments):
            report_data.append({
                'opportunity': {
                    'notice_id': opp.notice_id,
                    'title': opp.title,
                    'buyer': opp.buyer_name,
                    'value_amount': opp.value_amount,
                    'matched_keywords': opp.matched_keywords,
                    'url': opp.url
                },
                'assessment': {
                    'is_relevant': assessment.is_relevant,
                    'confidence_score': assessment.confidence_score,
                    'reasoning': assessment.reasoning,
                    'category': assessment.category,
                    'false_positive_indicators': assessment.false_positive_indicators
                }
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 Assessment report saved to: {filename}")
        return filename

# Alternative: Local LLM using Ollama (no API key required)
class LocalLLMRelevanceChecker:
    """Local LLM checker using Ollama (runs on your machine)"""
    
    def __init__(self, model: str = "llama3.1"):
        self.model = model
        import requests
        self.session = requests.Session()
        
        # Test if Ollama is running
        try:
            response = self.session.get("http://localhost:11434/api/tags")
            if response.status_code != 200:
                raise Exception("Ollama not running")
        except:
            print("⚠️  Ollama not detected. Install Ollama and run 'ollama pull llama3.1' to use local LLM")
            raise Exception("Ollama not available")
    
    def assess_opportunity(self, opportunity: TenderOpportunity) -> RelevanceAssessment:
        """Assess using local Ollama model"""
        
        content = f"""
Assess this UK procurement opportunity for Net Zero/climate relevance:

TITLE: {opportunity.title}
BUYER: {opportunity.buyer_name}
MATCHED KEYWORDS: {', '.join(opportunity.matched_keywords)}
DESCRIPTION: {opportunity.description[:800]}

Is this genuinely related to Net Zero, renewable energy, sustainability, or climate action?
Consider if it's a false positive (like "environment" meaning office environment).

Respond with JSON:
{{"is_relevant": true/false, "confidence": 0-100, "reasoning": "brief explanation", "category": "Energy/Transport/Buildings/etc or null"}}
"""

        payload = {
            "model": self.model,
            "prompt": content,
            "stream": False,
            "options": {
                "temperature": 0.1,
                "top_p": 0.9
            }
        }
        
        try:
            response = self.session.post(
                "http://localhost:11434/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '')
                
                # Extract JSON from response
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                if json_match:
                    parsed = json.loads(json_match.group())
                    
                    return RelevanceAssessment(
                        is_relevant=parsed.get('is_relevant', False),
                        confidence_score=float(parsed.get('confidence', 0)),
                        reasoning=parsed.get('reasoning', ''),
                        category=parsed.get('category'),
                        false_positive_indicators=[]
                    )
            
            # Fallback
            return RelevanceAssessment(
                is_relevant=False,
                confidence_score=0,
                reasoning="Failed to parse local LLM response",
                category=None,
                false_positive_indicators=["Local LLM parsing error"]
            )
            
        except Exception as e:
            return RelevanceAssessment(
                is_relevant=False,
                confidence_score=0,
                reasoning=f"Local LLM error: {str(e)}",
                category=None,
                false_positive_indicators=["Local LLM error"]
            )

def demo_llm_checking():
    """Demo of LLM relevance checking"""
    print("🤖 LLM RELEVANCE CHECKER DEMO")
    print("=" * 50)
    
    # Try local LLM first, fallback to OpenAI
    try:
        checker = LocalLLMRelevanceChecker()
        print("Using local Ollama LLM")
    except:
        # You'll need to set your OpenAI API key
        checker = LLMRelevanceChecker()
        print("Using OpenAI LLM (requires API key)")
    
    # Example opportunities (you can replace with real data)
    from net_zero_tender_finder import NetZeroTenderFinder
    
    finder = NetZeroTenderFinder()
    opportunities = finder.search_opportunities(days_back=7, max_results=10)
    
    if opportunities:
        relevant_opps, assessments = checker.assess_batch(opportunities, confidence_threshold=60.0)
        
        # Save report
        checker.save_assessment_report(opportunities, assessments)
        
        print(f"\n✅ Found {len(relevant_opps)} relevant opportunities after LLM filtering")
    else:
        print("No opportunities found to assess")

if __name__ == "__main__":
    demo_llm_checking() 