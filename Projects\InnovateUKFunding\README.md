# Enhanced Funding Opportunities Dashboard

A comprehensive web scraping and analysis system for UK funding opportunities, combining data from multiple sources with an integrated dashboard interface.

## 🌟 Features

### Multi-Source Data Collection
- **Innovate UK Business Connect**: Scrapes funding opportunities from https://iuk-business-connect.org.uk/opportunities/
- **Net Zero Tenders**: Searches sustainability-focused opportunities from UK Find a Tender service
- **Unified Data Format**: Combines data from both sources into a standardized structure

### Enhanced Web Dashboard
- **Interactive Interface**: Modern web dashboard with real-time scraper controls
- **Source Identification**: Clear labeling of which website each opportunity came from
- **Advanced Filtering**: Filter by data source, sustainability focus, funding level, and search terms
- **Live Scraper Status**: Real-time status indicators and logging for scraper operations
- **Data Export**: Export filtered results to CSV format

### Improved Data Processing
- **Currency Handling**: Fixed issue with $ amounts not being categorized correctly
- **Funding Categorization**: Automatic categorization into funding ranges (Under £100k, £100k-£1M, etc.)
- **Sustainability Detection**: Automatic classification of sustainability/Net Zero related opportunities
- **Data Persistence**: Saves data in both JSON and CSV formats with timestamps
- **Smart Deduplication**: Automatically prevents duplicate opportunities from being added
- **Title Cleaning**: Removes concatenated content from scraped titles for cleaner data
- **Master Data Management**: Maintains a master dataset that only grows with new opportunities

## 🚀 Quick Start

### Installation

1. **Clone or download this repository**
2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

### Running the Dashboard

**Option 1: Easy Start (Recommended)**
```bash
python start_dashboard.py
```
This will automatically start the server and open your browser to the dashboard.

**Option 2: Manual Start**
```bash
python api_server.py
```
Then open your browser to http://localhost:5000

### Using the Dashboard

1. **Run Scrapers**: Use the buttons to run individual scrapers or both together
2. **View Results**: Data is automatically loaded and displayed in the table
3. **Filter Data**: Use the filter controls to narrow down results
4. **Export Data**: Click "Export to CSV" to download filtered results

## 📁 Project Structure

```
InnovateUKFunding/
├── enhanced_funding_dashboard.html    # Main web dashboard
├── api_server.py                     # Flask API server
├── funding_data_manager.py           # Unified data management with deduplication
├── run_combined_scrapers.py          # Combined scraper runner
├── iuk_scraper.py                    # Innovate UK scraper (enhanced)
├── net_zero_tender_finder.py         # Net Zero tender scraper
├── start_dashboard.py                # Easy startup script
├── data_utils.py                     # Data management utilities
├── test_deduplication.py             # Deduplication testing script
├── requirements.txt                  # Python dependencies
├── funding_opportunities_master.json # Master dataset (auto-created)
├── funding_opportunities_table.html  # Original dashboard (legacy)
└── README.md                         # This file
```

## 🔧 API Endpoints

The system provides a REST API for programmatic access:

- `GET /` - Main dashboard interface
- `POST /api/run-iuk-scraper` - Run Innovate UK scraper
- `POST /api/run-tender-scraper` - Run Net Zero tender scraper
- `POST /api/run-all-scrapers` - Run both scrapers and combine results
- `GET /api/get-latest-data` - Retrieve latest scraped data
- `GET /api/status` - Get scraper status information

## 📊 Data Fields

### Unified Data Structure
All opportunities are converted to a standardized format with these fields:

**Core Information**
- `id`: Unique identifier
- `title`: Opportunity title
- `url`: Link to full details
- `source`: Data source ('innovate_uk' or 'net_zero_tenders')

**Dates**
- `publication_date`: When opportunity was published
- `opens_date`: When applications open
- `closes_date`: Application deadline
- `submission_deadline`: Final submission deadline

**Organization**
- `organisation`: Funding organization (IUK)
- `buyer_name`: Buyer organization (Tenders)
- `contact_email`: Contact email
- `contact_phone`: Contact phone

**Funding**
- `funding_amount`: Funding amount as string
- `currency`: Currency symbol
- `funding_category`: Categorized funding level
- `value_amount`: Numeric funding value
- `value_currency`: Currency code

**Content**
- `description`: Full description
- `brief_description`: Short summary
- `is_sustainable`: Boolean sustainability flag
- `matched_keywords`: Sustainability keywords found
- `sectors`: Relevant industry sectors

**Process Details**
- `status`: Current status
- `stage`: Process stage
- `procedure_type`: Type of procedure
- `award_criteria`: Assessment criteria
- `contract_duration`: Contract length

## 🚫 Deduplication System

The system now includes intelligent deduplication to prevent adding the same opportunity multiple times:

### How It Works
- **Master Dataset**: Maintains `funding_opportunities_master.json` with all unique opportunities
- **Hash-Based Detection**: Uses cleaned title + URL + source to generate unique hashes
- **Title Cleaning**: Automatically removes concatenated content (dates, descriptions) from titles
- **Smart Comparison**: Considers opportunities duplicate if they have the same cleaned title and URL

### Usage
When you run the scrapers, the system will:
1. Load existing opportunities from the master file
2. Clean titles from newly scraped data
3. Compare against existing data using hashes
4. Only add truly new opportunities to the master dataset
5. Report how many new vs duplicate opportunities were found

### Example Output
```
🔍 Checking for duplicates against existing data...
Processed 25 opportunities:
  - New: 3
  - Duplicates: 22
```

### Data Management Tools

**View Master Data Statistics**:
```bash
python data_utils.py
# Choose option 1 to see statistics
```

**Search Opportunities**:
```bash
python data_utils.py
# Choose option 2 and enter search terms
```

**Clean All Titles**:
```bash
python data_utils.py
# Choose option 3 to clean all titles in master data
```

**Reset Master Data** (creates backup):
```bash
python data_utils.py
# Choose option 4 to reset and start fresh
```

### Testing Deduplication
```bash
python test_deduplication.py
```

## 🔍 Advanced Usage

### Running Individual Scrapers

**Innovate UK Scraper Only**:
```python
from iuk_scraper import InnovateUKScraper

scraper = InnovateUKScraper()
opportunities = scraper.scrape_all(include_details=True)
scraper.save_to_json(opportunities, "iuk_results.json")
```

**Net Zero Tender Scraper Only**:
```python
from net_zero_tender_finder import NetZeroTenderFinder

finder = NetZeroTenderFinder()
opportunities = finder.search_opportunities(days_back=14)
finder.save_to_csv(opportunities, "tender_results.csv")
```

**Combined Scraping**:
```python
from run_combined_scrapers import main
main()  # Runs both scrapers and combines results
```

### Data Management

```python
from funding_data_manager import FundingDataManager

manager = FundingDataManager()

# Convert IUK data to unified format
unified_opp = manager.convert_iuk_opportunity(iuk_data)

# Convert tender data to unified format
unified_opp = manager.convert_tender_opportunity(tender_data)

# Save combined data
json_file, csv_file = manager.save_opportunities(opportunities)
```

## 🛠️ Configuration

### Scraper Settings

You can modify scraper behavior by editing the configuration in the respective files:

**IUK Scraper** (`iuk_scraper.py`):
- `max_pages`: Maximum pages to scrape (default: 5)
- `include_details`: Whether to scrape detailed information
- `max_opportunities`: Limit number of opportunities

**Tender Scraper** (`net_zero_tender_finder.py`):
- `days_back`: How many days back to search (default: 14)
- `max_results`: Maximum results to process (default: 500)
- `keywords`: List of sustainability keywords to search for

### API Server Settings

**Port Configuration** (`api_server.py`):
```python
app.run(debug=True, host='0.0.0.0', port=5000)
```

## 🔧 Troubleshooting

### Common Issues

1. **Dependencies Missing**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Port Already in Use**:
   - Change the port in `api_server.py`
   - Or kill the process using port 5000

3. **No Data Found**:
   - Check internet connection
   - Website structure may have changed
   - Check scraper logs for errors

4. **Browser Not Opening**:
   - Manually navigate to http://localhost:5000
   - Use `python start_dashboard.py --no-browser`

### Debug Mode

To run in debug mode with detailed logging:
```bash
python api_server.py
```
Check the console output for detailed error messages.

## 📈 Performance Notes

- **IUK Scraper**: Takes 2-5 minutes for full scrape with details
- **Tender Scraper**: Takes 1-3 minutes depending on date range
- **Combined Run**: Takes 3-8 minutes total
- **Memory Usage**: Typically under 100MB for normal datasets

## 🔒 Legal and Ethical Considerations

- **Respectful Scraping**: Built-in delays and rate limiting
- **Terms of Service**: Ensure compliance with website terms
- **Data Usage**: For research and analysis purposes
- **Rate Limiting**: Automatic delays between requests
- **Error Handling**: Graceful handling of failures

## 🤝 Contributing

To contribute to this project:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is for educational and research purposes. Please respect the terms of service of the scraped websites.

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section
2. Review the console logs
3. Ensure all dependencies are installed
4. Check that the websites are accessible

For additional help, please create an issue with:
- Error messages
- Steps to reproduce
- System information
- Log output
