#!/usr/bin/env python3
"""
Enhanced Scrapers with Smart Date Filtering, Better Keyword Matching, and Human-in-the-Loop Review
"""

import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from funding_data_manager import FundingDataManager, FundingOpportunity
from iuk_scraper import InnovateUKScraper
from net_zero_tender_finder import NetZeroTenderFinder, TenderOpportunity

class EnhancedIUKScraper(InnovateUKScraper):
    """Enhanced IUK Scraper with date-aware scraping"""
    
    def __init__(self, base_url="https://iuk-business-connect.org.uk/opportunities/"):
        super().__init__(base_url)
        self.last_scrape_file = Path("last_iuk_scrape.json")
        
    def should_scrape_today(self) -> bool:
        """Check if we should scrape based on last scrape date"""
        if not self.last_scrape_file.exists():
            return True
            
        try:
            with open(self.last_scrape_file, 'r') as f:
                data = json.load(f)
            
            last_scrape = datetime.fromisoformat(data['last_scrape_date'])
            today = datetime.now().date()
            
            # Only scrape if we haven't scraped today
            return last_scrape.date() < today
        except:
            return True
    
    def update_last_scrape_date(self, opportunity_count: int):
        """Update the last scrape date and stats"""
        data = {
            'last_scrape_date': datetime.now().isoformat(),
            'opportunities_found': opportunity_count,
            'scrape_successful': True
        }
        
        with open(self.last_scrape_file, 'w') as f:
            json.dump(data, f, indent=2)
    
    def smart_scrape(self, force_rescrape: bool = False) -> List[Dict]:
        """Smart scraping that respects date constraints"""
        if not force_rescrape and not self.should_scrape_today():
            print("📅 IUK scraping skipped - already scraped today")
            print("💡 Use force_rescrape=True to override this behavior")
            return []
        
        print("🔍 Running IUK scraper...")
        opportunities = self.scrape_all(include_details=True)
        
        # Update last scrape date
        self.update_last_scrape_date(len(opportunities))
        
        return opportunities

class EnhancedNetZeroFinder(NetZeroTenderFinder):
    """Enhanced Net Zero Finder with better keyword matching and relevance scoring"""
    
    def __init__(self):
        super().__init__()
        
        # More specific keyword categories for better matching
        self.energy_keywords = [
            "renewable energy", "clean energy", "green energy", "sustainable energy",
            "solar power", "wind energy", "wind power", "hydroelectric", "geothermal",
            "energy efficiency", "energy saving", "energy storage", "battery technology",
            "grid modernisation", "smart grid", "microgrid"
        ]
        
        self.transport_keywords = [
            "electric vehicle", "ev charging", "sustainable transport", "active travel",
            "cycling infrastructure", "public transport electrification", "rail electrification",
            "zero emission vehicle", "ultra low emission"
        ]
        
        self.buildings_keywords = [
            "heat pump", "heat pumps", "building retrofit", "insulation", "thermal efficiency",
            "sustainable construction", "green building", "passive house", "district heating",
            "heat networks", "building energy management"
        ]
        
        self.climate_keywords = [
            "net zero", "net-zero", "carbon neutral", "carbon neutrality", "decarbonisation",
            "carbon reduction", "emissions reduction", "greenhouse gas", "ghg reduction",
            "climate change", "climate action", "climate resilience", "carbon offset"
        ]
        
        self.environment_keywords = [
            "environmental sustainability", "environmental improvement", "biodiversity",
            "nature recovery", "ecological restoration", "air quality improvement",
            "pollution reduction", "waste reduction", "circular economy"
        ]
        
        # Context-aware exclusions - these should be excluded when found with certain terms
        self.contextual_exclusions = {
            "environment": ["home environment", "work environment", "office environment", 
                          "classroom environment", "learning environment", "social environment"],
            "green": ["green space design", "green belt", "bowling green", "village green"],
            "sustainable": ["sustainable business model", "sustainable growth", "sustainable finance"]
        }
        
        # High relevance indicators
        self.high_relevance_indicators = [
            "carbon", "emission", "renewable", "zero", "climate", "energy efficiency",
            "sustainability", "environmental impact", "decarbonisation"
        ]
    
    def calculate_relevance_score(self, title: str, description: str, matched_keywords: List[str]) -> Tuple[float, Dict]:
        """Calculate a relevance score for sustainability/net zero context"""
        text = f"{title} {description}".lower()
        
        scoring_details = {
            "keyword_matches": len(matched_keywords),
            "high_relevance_matches": 0,
            "category_diversity": 0,
            "context_penalties": 0,
            "final_score": 0.0
        }
        
        # Base score from keyword matches
        base_score = len(matched_keywords) * 10
        
        # Bonus for high relevance indicators
        high_rel_count = sum(1 for indicator in self.high_relevance_indicators if indicator in text)
        scoring_details["high_relevance_matches"] = high_rel_count
        base_score += high_rel_count * 15
        
        # Bonus for category diversity
        categories_matched = 0
        if any(kw in text for kw in self.energy_keywords):
            categories_matched += 1
        if any(kw in text for kw in self.transport_keywords):
            categories_matched += 1
        if any(kw in text for kw in self.buildings_keywords):
            categories_matched += 1
        if any(kw in text for kw in self.climate_keywords):
            categories_matched += 1
        if any(kw in text for kw in self.environment_keywords):
            categories_matched += 1
        
        scoring_details["category_diversity"] = categories_matched
        base_score += categories_matched * 5
        
        # Penalties for contextual mismatches
        penalty = 0
        for keyword, exclusion_contexts in self.contextual_exclusions.items():
            if keyword in text:
                for context in exclusion_contexts:
                    if context in text:
                        penalty += 20
                        scoring_details["context_penalties"] += 1
        
        final_score = max(0, base_score - penalty)
        scoring_details["final_score"] = final_score
        
        return final_score, scoring_details
    
    def enhanced_process_release(self, release: Dict) -> Optional[Tuple[TenderOpportunity, float, Dict]]:
        """Process release with relevance scoring"""
        opportunity = self._process_release(release)
        
        if opportunity:
            score, details = self.calculate_relevance_score(
                opportunity.title, 
                opportunity.description, 
                opportunity.matched_keywords
            )
            return opportunity, score, details
        
        return None
    
    def search_with_relevance_scoring(self, days_back: int = 7, max_results: int = 500, 
                                    min_relevance_score: float = 30.0) -> List[Tuple[TenderOpportunity, float, Dict]]:
        """Search opportunities with relevance scoring"""
        print(f"🔍 Searching with relevance scoring (min score: {min_relevance_score})...")
        
        # Use the parent class method but process differently
        opportunities = []
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        print(f"Searching from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Build API parameters
        params = {
            'stages': ','.join(self.stages),
            'updatedFrom': start_date.strftime('%Y-%m-%dT00:00:00'),
            'updatedTo': end_date.strftime('%Y-%m-%dT23:59:59'),
            'limit': 100
        }
        
        total_processed = 0
        cursor = None
        
        while total_processed < max_results:
            if cursor:
                params['cursor'] = cursor
                
            try:
                response = self.session.get(self.base_url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if 'releases' not in data or not data['releases']:
                    break
                
                # Process each release with scoring
                for release in data['releases']:
                    result = self.enhanced_process_release(release)
                    if result:
                        opportunity, score, details = result
                        if score >= min_relevance_score:
                            opportunities.append((opportunity, score, details))
                
                total_processed += len(data['releases'])
                cursor = self._extract_next_cursor(response.headers)
                
                if not cursor:
                    break
                    
            except Exception as e:
                print(f"Error: {e}")
                break
        
        # Sort by relevance score
        opportunities.sort(key=lambda x: x[1], reverse=True)
        
        print(f"Found {len(opportunities)} highly relevant opportunities")
        return opportunities

class HumanInTheLoopReviewer:
    """Human-in-the-loop system for reviewing and validating opportunities"""
    
    def __init__(self, data_dir: str = "."):
        self.data_dir = Path(data_dir)
        self.review_file = self.data_dir / "opportunity_reviews.json"
        self.load_reviews()
    
    def load_reviews(self):
        """Load existing reviews"""
        if self.review_file.exists():
            try:
                with open(self.review_file, 'r') as f:
                    self.reviews = json.load(f)
            except:
                self.reviews = {}
        else:
            self.reviews = {}
    
    def save_reviews(self):
        """Save reviews to file"""
        with open(self.review_file, 'w') as f:
            json.dump(self.reviews, f, indent=2)
    
    def get_review_status(self, opportunity_id: str) -> Optional[str]:
        """Get review status for an opportunity"""
        return self.reviews.get(opportunity_id, {}).get('status')
    
    def review_opportunity(self, opportunity: TenderOpportunity, score: float, details: Dict) -> str:
        """Present opportunity for human review"""
        opp_id = opportunity.notice_id
        
        # Check if already reviewed
        existing_review = self.get_review_status(opp_id)
        if existing_review:
            return existing_review
        
        print(f"\n" + "="*80)
        print(f"🔍 OPPORTUNITY REVIEW")
        print(f"="*80)
        print(f"Title: {opportunity.title}")
        print(f"Buyer: {opportunity.buyer_name}")
        print(f"Relevance Score: {score:.1f}/100")
        print(f"Matched Keywords: {', '.join(opportunity.matched_keywords)}")
        print(f"Value: {opportunity.value_currency} {opportunity.value_amount:,.2f}" if opportunity.value_amount else "Value: Not specified")
        print(f"Closing Date: {opportunity.closing_date}" if opportunity.closing_date else "Closing Date: Not specified")
        print(f"\nDescription (first 300 chars):")
        print(f"{opportunity.description[:300]}...")
        print(f"\nURL: {opportunity.url}")
        
        print(f"\nScoring Details:")
        print(f"  - Keyword matches: {details['keyword_matches']}")
        print(f"  - High relevance matches: {details['high_relevance_matches']}")
        print(f"  - Category diversity: {details['category_diversity']}")
        print(f"  - Context penalties: {details['context_penalties']}")
        
        print(f"\n" + "-"*80)
        print("Is this opportunity relevant to Net Zero/Sustainability?")
        print("r = Relevant (include)")
        print("n = Not relevant (exclude)")
        print("s = Skip (review later)")
        print("q = Quit review process")
        
        while True:
            choice = input("Your choice (r/n/s/q): ").lower().strip()
            
            if choice == 'r':
                status = 'relevant'
                break
            elif choice == 'n':
                status = 'not_relevant'
                break
            elif choice == 's':
                status = 'skip'
                break
            elif choice == 'q':
                return 'quit'
            else:
                print("Please enter r, n, s, or q")
        
        # Save review
        self.reviews[opp_id] = {
            'status': status,
            'reviewed_at': datetime.now().isoformat(),
            'score': score,
            'title': opportunity.title
        }
        self.save_reviews()
        
        return status
    
    def review_batch(self, opportunities_with_scores: List[Tuple[TenderOpportunity, float, Dict]], 
                    auto_accept_threshold: float = 70.0) -> List[TenderOpportunity]:
        """Review a batch of opportunities"""
        approved_opportunities = []
        
        print(f"\n🔍 STARTING HUMAN REVIEW PROCESS")
        print(f"Auto-accepting opportunities with score ≥ {auto_accept_threshold}")
        print(f"Total opportunities to review: {len(opportunities_with_scores)}")
        
        for i, (opportunity, score, details) in enumerate(opportunities_with_scores, 1):
            print(f"\nProgress: {i}/{len(opportunities_with_scores)}")
            
            # Auto-accept very high scores
            if score >= auto_accept_threshold:
                print(f"✅ Auto-accepted (score: {score:.1f}): {opportunity.title[:60]}...")
                approved_opportunities.append(opportunity)
                
                # Still save the review
                self.reviews[opportunity.notice_id] = {
                    'status': 'relevant',
                    'reviewed_at': datetime.now().isoformat(),
                    'score': score,
                    'title': opportunity.title,
                    'auto_accepted': True
                }
                continue
            
            # Human review required
            review_result = self.review_opportunity(opportunity, score, details)
            
            if review_result == 'relevant':
                approved_opportunities.append(opportunity)
            elif review_result == 'quit':
                print("Review process stopped by user")
                break
        
        self.save_reviews()
        print(f"\n✅ Review complete. Approved {len(approved_opportunities)} opportunities")
        
        return approved_opportunities

def run_smart_scrapers(force_iuk_rescrape: bool = False, 
                      human_review: bool = False,
                      min_relevance_score: float = 30.0,
                      auto_accept_threshold: float = 70.0) -> Tuple[List[Dict], List[TenderOpportunity]]:
    """Run scrapers with smart date filtering and optional human review"""
    
    print("🚀 SMART SCRAPER SYSTEM")
    print("=" * 50)
    
    # IUK Scraping with date awareness
    iuk_scraper = EnhancedIUKScraper()
    iuk_data = iuk_scraper.smart_scrape(force_rescrape=force_iuk_rescrape)
    
    # Net Zero scraping with relevance scoring
    nz_finder = EnhancedNetZeroFinder()
    nz_opportunities_with_scores = nz_finder.search_with_relevance_scoring(
        days_back=14, 
        max_results=500,
        min_relevance_score=min_relevance_score
    )
    
    # Extract just the opportunities for now
    nz_opportunities = [opp for opp, score, details in nz_opportunities_with_scores]
    
    # Optional human review
    if human_review and nz_opportunities_with_scores:
        print(f"\n🧑‍💼 HUMAN REVIEW REQUESTED")
        reviewer = HumanInTheLoopReviewer()
        nz_opportunities = reviewer.review_batch(
            nz_opportunities_with_scores,
            auto_accept_threshold=auto_accept_threshold
        )
    
    return iuk_data, nz_opportunities

if __name__ == "__main__":
    # Example usage
    iuk_data, nz_data = run_smart_scrapers(
        force_iuk_rescrape=False,
        human_review=True,
        min_relevance_score=25.0,
        auto_accept_threshold=60.0
    )
    
    print(f"\nFinal Results:")
    print(f"IUK Opportunities: {len(iuk_data)}")
    print(f"Net Zero Opportunities: {len(nz_data)}") 