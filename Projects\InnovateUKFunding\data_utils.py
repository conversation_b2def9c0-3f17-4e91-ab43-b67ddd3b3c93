#!/usr/bin/env python3
"""
Data Management Utilities
Helper functions for managing funding opportunities data
"""

import json
from pathlib import Path
from datetime import datetime
from funding_data_manager import FundingDataManager, FundingOpportunity

def view_master_data_stats():
    """View statistics about the master data file"""
    manager = FundingDataManager()
    
    if not manager.master_data_file.exists():
        print("❌ No master data file found. Run the scraper first to create it.")
        return
    
    opportunities, _ = manager.load_existing_opportunities()
    
    print(f"📊 MASTER DATA STATISTICS")
    print(f"=" * 50)
    print(f"Total opportunities: {len(opportunities)}")
    
    # By source
    source_counts = {}
    for opp in opportunities:
        source_counts[opp.source] = source_counts.get(opp.source, 0) + 1
    
    print(f"\nBy source:")
    for source, count in source_counts.items():
        source_name = "Innovate UK" if source == "innovate_uk" else "Net Zero Tenders"
        print(f"  {source_name}: {count}")
    
    # By sustainability
    sustainable_count = sum(1 for opp in opportunities if opp.is_sustainable)
    print(f"\nSustainability-related: {sustainable_count} ({sustainable_count/len(opportunities)*100:.1f}%)")
    
    # Recent additions
    recent_opportunities = sorted(opportunities, key=lambda x: x.scraped_at or "", reverse=True)[:5]
    print(f"\nMost recently added:")
    for i, opp in enumerate(recent_opportunities, 1):
        print(f"  {i}. {opp.title[:60]}...")
        print(f"     Source: {'IUK' if opp.source == 'innovate_uk' else 'Tender'}")
        print(f"     Scraped: {opp.scraped_at}")

def search_opportunities(query: str, limit: int = 10):
    """Search through the master data for opportunities matching the query"""
    manager = FundingDataManager()
    
    if not manager.master_data_file.exists():
        print("❌ No master data file found. Run the scraper first to create it.")
        return
    
    opportunities, _ = manager.load_existing_opportunities()
    query_lower = query.lower()
    
    matching = []
    for opp in opportunities:
        # Search in title, description, and organisation
        search_text = f"{opp.title} {opp.description or ''} {opp.organisation or ''}".lower()
        if query_lower in search_text:
            matching.append(opp)
    
    print(f"🔍 SEARCH RESULTS for '{query}'")
    print(f"=" * 50)
    print(f"Found {len(matching)} matching opportunities")
    
    for i, opp in enumerate(matching[:limit], 1):
        print(f"\n{i}. {opp.title}")
        print(f"   Source: {'Innovate UK' if opp.source == 'innovate_uk' else 'Net Zero Tenders'}")
        print(f"   URL: {opp.url}")
        if opp.closes_date:
            print(f"   Closes: {opp.closes_date}")
        if opp.funding_category:
            print(f"   Funding: {opp.funding_category}")

def clean_master_data():
    """Clean and re-process all titles in the master data"""
    manager = FundingDataManager()
    
    if not manager.master_data_file.exists():
        print("❌ No master data file found. Run the scraper first to create it.")
        return
    
    opportunities, _ = manager.load_existing_opportunities()
    
    print(f"🧹 CLEANING MASTER DATA")
    print(f"Processing {len(opportunities)} opportunities...")
    
    cleaned_count = 0
    for opp in opportunities:
        original_title = opp.title
        cleaned_title = manager.clean_title_for_deduplication(opp.title)
        
        if cleaned_title != original_title and cleaned_title:
            opp.title = cleaned_title
            cleaned_count += 1
            print(f"  Cleaned: {original_title[:50]}... -> {cleaned_title[:50]}...")
    
    if cleaned_count > 0:
        # Save the cleaned data
        backup_file = manager.data_dir / f"master_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        print(f"📋 Creating backup: {backup_file}")
        
        # Create backup
        original_data = json.loads(manager.master_data_file.read_text())
        backup_file.write_text(json.dumps(original_data, indent=2))
        
        # Save cleaned data
        manager.save_master_data(opportunities)
        print(f"✅ Cleaned {cleaned_count} titles and saved to master data")
    else:
        print("✅ No titles needed cleaning")

def reset_master_data():
    """Reset the master data file (use with caution!)"""
    manager = FundingDataManager()
    
    if manager.master_data_file.exists():
        backup_file = manager.data_dir / f"master_backup_before_reset_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        print(f"📋 Creating backup before reset: {backup_file}")
        
        # Create backup
        manager.master_data_file.rename(backup_file)
        print("✅ Master data file reset. Next scraper run will start fresh.")
    else:
        print("ℹ️  No master data file to reset.")

def main():
    """Interactive CLI for data management"""
    print("🌱 Funding Opportunities Data Manager")
    print("=" * 50)
    
    while True:
        print("\nAvailable commands:")
        print("1. View master data statistics")
        print("2. Search opportunities")
        print("3. Clean all titles in master data")
        print("4. Reset master data (creates backup)")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            view_master_data_stats()
        elif choice == "2":
            query = input("Enter search query: ").strip()
            if query:
                search_opportunities(query)
        elif choice == "3":
            confirm = input("This will clean all titles. Continue? (y/n): ").strip().lower()
            if confirm == 'y':
                clean_master_data()
        elif choice == "4":
            confirm = input("This will reset the master data file. Continue? (y/n): ").strip().lower()
            if confirm == 'y':
                reset_master_data()
        elif choice == "5":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == "__main__":
    main() 