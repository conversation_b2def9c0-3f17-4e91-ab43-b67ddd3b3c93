#!/usr/bin/env python3
"""
Funding Data Manager
Unified data structure and management for funding opportunities from multiple sources
"""

import json
import csv
import re
from datetime import datetime
from typing import List, Dict, Optional, Union, Set, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

@dataclass
class FundingOpportunity:
    """Unified data structure for funding opportunities from any source"""
    # Core identification
    id: str  # Unique identifier
    title: str
    url: str
    source: str  # 'innovate_uk' or 'net_zero_tenders'
    
    # Dates
    publication_date: Optional[str] = None
    opens_date: Optional[str] = None
    closes_date: Optional[str] = None
    submission_deadline: Optional[str] = None
    
    # Organization info
    organisation: Optional[str] = None
    buyer_name: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    
    # Funding details
    funding_amount: Optional[str] = None
    currency: Optional[str] = None
    funding_category: Optional[str] = None
    value_amount: Optional[float] = None
    value_currency: Optional[str] = None
    
    # Content
    description: Optional[str] = None
    brief_description: Optional[str] = None
    
    # Classification
    is_sustainable: Optional[bool] = None
    matched_keywords: Optional[List[str]] = None
    sectors: Optional[List[str]] = None
    cpv_codes: Optional[List[str]] = None
    
    # Process details
    status: Optional[str] = None
    stage: Optional[str] = None
    procedure_type: Optional[str] = None
    award_criteria: Optional[List[str]] = None
    contract_duration: Optional[str] = None
    
    # Additional details (for IUK opportunities)
    programme_type: Optional[str] = None
    award_info: Optional[str] = None
    background: Optional[str] = None
    challenge_description: Optional[str] = None
    requirements: Optional[List[str]] = None
    assessment_criteria: Optional[List[str]] = None
    timeline: Optional[List[str]] = None
    application_process: Optional[str] = None
    ip_arrangements: Optional[str] = None
    eligibility: Optional[str] = None
    
    # Metadata
    scraped_at: Optional[str] = None
    page_found: Optional[int] = None

class FundingDataManager:
    """Manager for unified funding opportunity data"""
    
    def __init__(self, data_dir: str = "."):
        self.data_dir = Path(data_dir)
        self.master_data_file = self.data_dir / "funding_opportunities_master.json"
        self.sustainability_keywords = [
            "net zero", "net-zero", "carbon neutral", "carbon neutrality",
            "sustainability", "sustainable", "climate change", "climate",
            "renewable energy", "clean energy", "green energy",
            "emissions reduction", "carbon reduction", "decarbonisation",
            "environment", "environmental", "air quality", "air pollution",
            "energy efficiency", "solar", "wind energy", "electric vehicle",
            "ev charging", "heat pump", "insulation", "retrofit",
            "circular economy", "bioeconomy", "natural resources"
        ]
    
    def clean_title_for_deduplication(self, title: str) -> str:
        """Clean title for better deduplication - similar to your IUK scraper cleaning"""
        if not title:
            return ""
        
        # Remove HTML tags
        title = re.sub(r'<[^>]+>', '', title)
        
        # Remove extra whitespace
        title = re.sub(r'\s+', ' ', title).strip()
        
        # Strategy: Find the first substantial part before contamination
        # Look for "Opens" as a clear delimiter
        opens_match = re.search(r'^(.*?)(?=Opens\s+\d{2}/\d{2}/\d{4})', title, flags=re.IGNORECASE)
        if opens_match:
            title = opens_match.group(1).strip()
        else:
            # Try other common delimiters
            delimiters = [
                r'^(.*?)(?=\d{2}/\d{2}/\d{4})',  # Before any date
                r'^(.*?)(?=More Information)',    # Before "More Information"
                r'^(.*?)(?=Read More)',          # Before "Read More"
                r'^(.*?)(?=Apply Now)',          # Before "Apply Now"
                r'^(.*?)(?=View Details)',       # Before "View Details"
            ]
            
            for delimiter in delimiters:
                match = re.search(delimiter, title, flags=re.IGNORECASE)
                if match and len(match.group(1).strip()) > 10:
                    title = match.group(1).strip()
                    break
        
        # Clean up the extracted title
        if title:
            # Remove trailing punctuation artifacts
            title = re.sub(r'[:\s]*$', '', title)
            
            # Remove common redundant phrases at the beginning
            title = re.sub(r'^(opportunity[:\s]*|funding[:\s]*|grant[:\s]*)', '', title, flags=re.IGNORECASE)
            
            # Final cleanup
            title = re.sub(r'\s+', ' ', title).strip()
            
            # Ensure first letter is capitalized
            if title and title[0].islower():
                title = title[0].upper() + title[1:]
        
        return title if title else ""
    
    def generate_opportunity_hash(self, opportunity: FundingOpportunity) -> str:
        """Generate a unique hash for an opportunity based on key characteristics"""
        # Use cleaned title, URL, and source for generating hash
        cleaned_title = self.clean_title_for_deduplication(opportunity.title)
        hash_string = f"{cleaned_title}|{opportunity.url}|{opportunity.source}"
        return hashlib.md5(hash_string.encode()).hexdigest()
    
    def load_existing_opportunities(self) -> Tuple[List[FundingOpportunity], Set[str]]:
        """Load existing opportunities and their hashes"""
        if not self.master_data_file.exists():
            return [], set()
        
        try:
            existing_opportunities = self.load_opportunities(str(self.master_data_file))
            existing_hashes = set()
            
            for opp in existing_opportunities:
                hash_val = self.generate_opportunity_hash(opp)
                existing_hashes.add(hash_val)
            
            return existing_opportunities, existing_hashes
        except Exception as e:
            print(f"Warning: Could not load existing opportunities: {e}")
            return [], set()
    
    def deduplicate_opportunities(self, new_opportunities: List[FundingOpportunity]) -> Tuple[List[FundingOpportunity], List[FundingOpportunity]]:
        """Deduplicate new opportunities against existing ones"""
        existing_opportunities, existing_hashes = self.load_existing_opportunities()
        
        truly_new = []
        duplicates = []
        
        for opp in new_opportunities:
            # Clean the title before saving
            opp.title = self.clean_title_for_deduplication(opp.title)
            
            # Generate hash for this opportunity
            opp_hash = self.generate_opportunity_hash(opp)
            
            if opp_hash not in existing_hashes:
                truly_new.append(opp)
                existing_hashes.add(opp_hash)  # Add to set to avoid duplicates within the new batch
            else:
                duplicates.append(opp)
        
        return truly_new, duplicates
    
    def save_master_data(self, all_opportunities: List[FundingOpportunity]) -> str:
        """Save all opportunities to the master data file"""
        data_dicts = [asdict(opp) for opp in all_opportunities]
        
        with open(self.master_data_file, 'w', encoding='utf-8') as f:
            json.dump(data_dicts, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"Master data updated: {self.master_data_file}")
        return str(self.master_data_file)
    
    def add_new_opportunities(self, new_opportunities: List[FundingOpportunity]) -> Tuple[List[FundingOpportunity], int, int]:
        """Add only new opportunities to the master dataset"""
        # Load existing opportunities
        existing_opportunities, _ = self.load_existing_opportunities()
        
        # Deduplicate
        truly_new, duplicates = self.deduplicate_opportunities(new_opportunities)
        
        if truly_new:
            # Combine existing and new opportunities
            all_opportunities = existing_opportunities + truly_new
            
            # Save updated master data
            self.save_master_data(all_opportunities)
            
            # Also save a timestamped backup of just the new opportunities
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.save_opportunities(truly_new, f"new_opportunities_{timestamp}")
        
        print(f"Processed {len(new_opportunities)} opportunities:")
        print(f"  - New: {len(truly_new)}")
        print(f"  - Duplicates: {len(duplicates)}")
        
        return truly_new, len(truly_new), len(duplicates)
    
    def categorize_funding_amount(self, amount_str: str, currency: str = None) -> str:
        """Categorize funding amount into standard ranges, handling multiple currencies"""
        if not amount_str or amount_str.lower() in ['see details', 'not specified', 'n/a']:
            return "No amount specified"
        
        # Extract numeric value and currency
        if not currency:
            currency_match = re.search(r'[£€$]', amount_str)
            currency = currency_match.group(0) if currency_match else '£'
        
        # Extract numeric value
        numeric_match = re.search(r'[\d,]+(?:\.\d+)?', amount_str.replace(',', ''))
        if not numeric_match:
            return "No amount specified"
        
        try:
            value = float(numeric_match.group(0).replace(',', ''))
        except ValueError:
            return "No amount specified"
        
        # Handle multipliers (k, m, b, million, thousand)
        amount_lower = amount_str.lower()
        if 'million' in amount_lower or 'm' in amount_lower:
            value *= 1_000_000
        elif 'thousand' in amount_lower or 'k' in amount_lower:
            value *= 1_000
        elif 'billion' in amount_lower or 'b' in amount_lower:
            value *= 1_000_000_000
        
        # Convert to GBP equivalent for categorization (approximate rates)
        if currency == '$':
            value *= 0.79  # USD to GBP
        elif currency == '€':
            value *= 0.85  # EUR to GBP
        
        # Categorize based on GBP equivalent
        if value >= 10_000_000:
            return "Over £10M"
        elif value >= 1_000_000:
            return "£1M - £10M"
        elif value >= 100_000:
            return "£100k - £1M"
        elif value > 0:
            return "Under £100k"
        else:
            return "No amount specified"
    
    def check_sustainability(self, text: str) -> bool:
        """Check if opportunity is sustainability-related based on keywords"""
        if not text:
            return False
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in self.sustainability_keywords)
    
    def convert_iuk_opportunity(self, iuk_data: Dict) -> FundingOpportunity:
        """Convert IUK scraper data to unified format"""
        # Determine sustainability
        search_text = f"{iuk_data.get('title', '')} {iuk_data.get('brief_description', '')} {iuk_data.get('description', '')} {iuk_data.get('challenge_description', '')}"
        is_sustainable = self.check_sustainability(search_text)
        
        # Get funding category
        funding_category = iuk_data.get('funding_category') or iuk_data.get('detailed_funding_category')
        if not funding_category:
            funding_amount = iuk_data.get('detailed_funding_amount') or iuk_data.get('funding_amount')
            currency = iuk_data.get('detailed_currency') or iuk_data.get('currency')
            if funding_amount:
                funding_category = self.categorize_funding_amount(funding_amount, currency)
            else:
                funding_category = "No amount specified"
        
        return FundingOpportunity(
            id=f"iuk_{hash(iuk_data.get('url', ''))}",
            title=iuk_data.get('title', ''),
            url=iuk_data.get('url', ''),
            source='innovate_uk',
            publication_date=iuk_data.get('scraped_at'),
            opens_date=iuk_data.get('opens_date') or iuk_data.get('registration_opens'),
            closes_date=iuk_data.get('closes_date') or iuk_data.get('registration_closes'),
            organisation=iuk_data.get('organisation'),
            funding_amount=iuk_data.get('detailed_funding_amount') or iuk_data.get('funding_amount'),
            currency=iuk_data.get('detailed_currency') or iuk_data.get('currency'),
            funding_category=funding_category,
            description=iuk_data.get('challenge_description'),
            brief_description=iuk_data.get('brief_description'),
            is_sustainable=is_sustainable,
            sectors=iuk_data.get('sectors', []),
            programme_type=iuk_data.get('programme_type'),
            award_info=iuk_data.get('award_info'),
            background=iuk_data.get('background'),
            challenge_description=iuk_data.get('challenge_description'),
            requirements=iuk_data.get('requirements', []),
            assessment_criteria=iuk_data.get('assessment_criteria', []),
            timeline=iuk_data.get('timeline', []),
            application_process=iuk_data.get('application_process'),
            ip_arrangements=iuk_data.get('ip_arrangements'),
            eligibility=iuk_data.get('eligibility'),
            scraped_at=iuk_data.get('scraped_at'),
            page_found=iuk_data.get('page_found')
        )
    
    def convert_tender_opportunity(self, tender_data) -> FundingOpportunity:
        """Convert Net Zero tender data to unified format"""
        # Handle both dict and dataclass inputs
        if hasattr(tender_data, '__dict__'):
            data = asdict(tender_data)
        else:
            data = tender_data
        
        # Get funding category
        funding_amount = None
        currency = data.get('value_currency', 'GBP')
        if data.get('value_amount'):
            funding_amount = f"{currency} {data['value_amount']:,.2f}"
        
        funding_category = self.categorize_funding_amount(funding_amount, currency) if funding_amount else "No amount specified"
        
        return FundingOpportunity(
            id=f"tender_{data.get('notice_id', '')}",
            title=data.get('title', ''),
            url=data.get('url', ''),
            source='net_zero_tenders',
            publication_date=data.get('publication_date'),
            closes_date=data.get('closing_date'),
            submission_deadline=data.get('submission_deadline'),
            buyer_name=data.get('buyer_name'),
            contact_email=data.get('buyer_contact_email'),
            contact_phone=data.get('buyer_contact_phone'),
            funding_amount=funding_amount,
            currency=currency,
            funding_category=funding_category,
            value_amount=data.get('value_amount'),
            value_currency=currency,
            description=data.get('description'),
            is_sustainable=True,  # All tender opportunities are sustainability-related
            matched_keywords=data.get('matched_keywords', []),
            cpv_codes=data.get('cpv_codes', []),
            status=data.get('status'),
            stage=data.get('stage'),
            procedure_type=data.get('procedure_type'),
            award_criteria=data.get('award_criteria', []),
            contract_duration=data.get('contract_duration'),
            scraped_at=datetime.now().isoformat()
        )
    
    def save_opportunities(self, opportunities: List[FundingOpportunity], 
                          filename_base: str = None) -> tuple[str, str]:
        """Save opportunities to both JSON and CSV formats"""
        if not filename_base:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename_base = f"combined_funding_opportunities_{timestamp}"
        
        json_file = self.data_dir / f"{filename_base}.json"
        csv_file = self.data_dir / f"{filename_base}.csv"
        
        # Convert to dictionaries for JSON
        data_dicts = [asdict(opp) for opp in opportunities]
        
        # Save JSON
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data_dicts, f, indent=2, ensure_ascii=False, default=str)
        
        # Save CSV
        if data_dicts:
            with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data_dicts[0].keys())
                writer.writeheader()
                for row in data_dicts:
                    # Convert lists to strings for CSV
                    csv_row = {}
                    for key, value in row.items():
                        if isinstance(value, list):
                            csv_row[key] = '; '.join(str(item) for item in value) if value else ''
                        else:
                            csv_row[key] = value
                    writer.writerow(csv_row)
        
        print(f"Data saved to {json_file} and {csv_file}")
        return str(json_file), str(csv_file)
    
    def load_opportunities(self, json_file: str) -> List[FundingOpportunity]:
        """Load opportunities from JSON file"""
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        opportunities = []
        for item in data:
            # Convert string lists back to lists
            for key, value in item.items():
                if key in ['matched_keywords', 'sectors', 'cpv_codes', 'requirements', 
                          'assessment_criteria', 'timeline', 'award_criteria'] and isinstance(value, str):
                    item[key] = [x.strip() for x in value.split(';') if x.strip()] if value else []
            
            opportunities.append(FundingOpportunity(**item))
        
        return opportunities
