#!/usr/bin/env python3
"""
Test script to demonstrate the deduplication functionality
"""

from funding_data_manager import FundingDataManager, FundingOpportunity
from datetime import datetime

def test_deduplication():
    """Test the deduplication system with sample data"""
    print("🧪 TESTING DEDUPLICATION SYSTEM")
    print("=" * 50)
    
    manager = FundingDataManager()
    
    # Create some test opportunities
    test_opportunities = [
        FundingOpportunity(
            id="test_1",
            title="Open Horizons - Open Call for funding of women-led start-upsOpens 01/07/2025Closes: 21/08/2025Women-led startups in deep tech and digital innovation can apply for up to €55,000 in equity-free funding.More Information",
            url="https://example.com/opportunity1",
            source="innovate_uk",
            scraped_at=datetime.now().isoformat()
        ),
        FundingOpportunity(
            id="test_2", 
            title="Clean Energy Innovation Challenge 2025",
            url="https://example.com/opportunity2",
            source="net_zero_tenders",
            scraped_at=datetime.now().isoformat()
        ),
        FundingOpportunity(
            id="test_3",
            title="Open Horizons - Open Call for funding of women-led start-ups",  # Same as first but cleaned
            url="https://example.com/opportunity1",  # Same URL
            source="innovate_uk",
            scraped_at=datetime.now().isoformat()
        )
    ]
    
    print(f"🔍 Testing with {len(test_opportunities)} opportunities:")
    for i, opp in enumerate(test_opportunities, 1):
        print(f"{i}. {opp.title[:60]}...")
        print(f"   URL: {opp.url}")
        print(f"   Source: {opp.source}")
    
    # Test title cleaning
    print(f"\n🧹 TITLE CLEANING TEST")
    print(f"-" * 30)
    
    messy_title = "Open Horizons - Open Call for funding of women-led start-upsOpens 01/07/2025Closes: 21/08/2025Women-led startups in deep tech and digital innovation can apply for up to €55,000 in equity-free funding.More Information"
    clean_title = manager.clean_title_for_deduplication(messy_title)
    
    print(f"Original: {messy_title}")
    print(f"Cleaned:  {clean_title}")
    
    # Test deduplication
    print(f"\n🔄 DEDUPLICATION TEST")
    print(f"-" * 30)
    
    # First run - all should be new
    new_opps_1, new_count_1, dup_count_1 = manager.add_new_opportunities(test_opportunities[:2])
    print(f"First run: {new_count_1} new, {dup_count_1} duplicates")
    
    # Second run - should find duplicates
    new_opps_2, new_count_2, dup_count_2 = manager.add_new_opportunities(test_opportunities)
    print(f"Second run: {new_count_2} new, {dup_count_2} duplicates")
    
    # Show final results
    all_opportunities, hashes = manager.load_existing_opportunities()
    print(f"\nFinal master data contains {len(all_opportunities)} opportunities:")
    for i, opp in enumerate(all_opportunities, 1):
        print(f"{i}. {opp.title}")
        print(f"   Hash: {manager.generate_opportunity_hash(opp)[:10]}...")

if __name__ == "__main__":
    test_deduplication() 