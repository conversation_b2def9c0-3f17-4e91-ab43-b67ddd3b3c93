<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Funding Opportunities Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .controls-section {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .scraper-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .scraper-panel {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }
        .scraper-panel h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .run-button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .run-button:hover {
            background-color: #2980b9;
        }
        .run-button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .run-all-button {
            background-color: #27ae60;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            width: 100%;
            margin-top: 15px;
        }
        .run-all-button:hover {
            background-color: #229954;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-idle { background-color: #95a5a6; }
        .status-running { background-color: #f39c12; animation: pulse 1s infinite; }
        .status-success { background-color: #27ae60; }
        .status-error { background-color: #e74c3c; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .summary {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-box {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .filters {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filter-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .export-btn {
            background-color: #27ae60;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 20px;
        }
        .export-btn:hover {
            background-color: #229954;
        }
        .table-container {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th {
            background-color: #34495e;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        td {
            padding: 10px 8px;
            border-bottom: 1px solid #ecf0f1;
            vertical-align: top;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .sustainable-yes {
            background-color: #d5f4e6;
            color: #27ae60;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .sustainable-no {
            background-color: #fadbd8;
            color: #e74c3c;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .source-iuk {
            background-color: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .source-tender {
            background-color: #cce5ff;
            color: #004085;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .funding-high { color: #27ae60; font-weight: bold; }
        .funding-medium { color: #f39c12; font-weight: bold; }
        .funding-low { color: #3498db; }
        .funding-none { color: #95a5a6; }
        .title-cell {
            max-width: 400px;
            word-wrap: break-word;
            line-height: 1.4;
            padding: 8px;
        }
        .title-cell .title-text {
            font-weight: 600;
            color: #2c3e50;
            display: block;
        }
        .url-cell {
            max-width: 200px;
            word-wrap: break-word;
        }
        .url-cell a {
            color: #3498db;
            text-decoration: none;
        }
        .url-cell a:hover {
            text-decoration: underline;
        }
        .results-count {
            margin: 10px 0;
            font-weight: bold;
            color: #2c3e50;
        }
        .log-output {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
            display: none;
        }
        .show-logs {
            background-color: #95a5a6;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .last-updated {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Enhanced Funding Opportunities Dashboard</h1>
        <p>Integrated scraping and analysis of funding opportunities from multiple sources</p>
    </div>

    <div class="controls-section">
        <h3>Scraper Controls</h3>
        <div class="scraper-controls">
            <div class="scraper-panel">
                <h3><span class="status-indicator status-idle" id="iuk-status"></span>Innovate UK Scraper</h3>
                <p>Scrapes opportunities from Innovate UK Business Connect</p>
                <button class="run-button" onclick="runIUKScraper()" id="iuk-btn">Run IUK Scraper</button>
                <button class="show-logs" onclick="toggleLogs('iuk-logs')">Show Logs</button>
                <div class="log-output" id="iuk-logs"></div>
            </div>
            
            <div class="scraper-panel">
                <h3><span class="status-indicator status-idle" id="tender-status"></span>Net Zero Tender Scraper</h3>
                <p>Scrapes sustainability opportunities from UK Find a Tender</p>
                <button class="run-button" onclick="runTenderScraper()" id="tender-btn">Run Tender Scraper</button>
                <button class="show-logs" onclick="toggleLogs('tender-logs')">Show Logs</button>
                <div class="log-output" id="tender-logs"></div>
            </div>
        </div>
        
        <button class="run-all-button" onclick="runAllScrapers()" id="run-all-btn">
            🚀 Run All Scrapers & Update Data
        </button>
        
        <div class="last-updated" id="last-updated">
            Last updated: Never
        </div>
    </div>

    <div class="summary" id="summary-section">
        <h3>Summary Statistics</h3>
        <div class="summary-stats" id="summary-stats">
            <div class="stat-box">
                <div class="stat-number" id="total-count">0</div>
                <div>Total Opportunities</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" id="iuk-count">0</div>
                <div>Innovate UK</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" id="tender-count">0</div>
                <div>Net Zero Tenders</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" id="sustainable-count">0</div>
                <div>Sustainability Related</div>
            </div>
        </div>
    </div>

    <div class="filters">
        <div class="filter-group">
            <label for="sourceFilter">Data Source:</label>
            <select id="sourceFilter" onchange="filterTable()">
                <option value="all">All Sources</option>
                <option value="innovate_uk">Innovate UK Only</option>
                <option value="net_zero_tenders">Net Zero Tenders Only</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="sustainabilityFilter">Sustainability:</label>
            <select id="sustainabilityFilter" onchange="filterTable()">
                <option value="all">All Opportunities</option>
                <option value="yes">Sustainability Related Only</option>
                <option value="no">Non-Sustainability Only</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="fundingFilter">Funding Level:</label>
            <select id="fundingFilter" onchange="filterTable()">
                <option value="all">All Funding Levels</option>
                <option value="Under £100k">Under £100k</option>
                <option value="£100k - £1M">£100k - £1M</option>
                <option value="£1M - £10M">£1M - £10M</option>
                <option value="Over £10M">Over £10M</option>
                <option value="No amount specified">No Amount Specified</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="searchFilter">Search:</label>
            <input type="text" id="searchFilter" placeholder="Search titles..." onkeyup="filterTable()">
        </div>
        
        <button class="export-btn" onclick="exportToCSV()">Export to CSV</button>
        <button class="export-btn" onclick="loadSampleData()">Load Sample Data</button>
    </div>

    <div class="results-count" id="resultsCount">No data loaded</div>

    <div class="table-container">
        <table id="opportunitiesTable">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Source</th>
                    <th>Sustainability</th>
                    <th>Funding Category</th>
                    <th>Funding Amount</th>
                    <th>Organisation</th>
                    <th>Closing Date</th>
                    <th>URL</th>
                </tr>
            </thead>
            <tbody id="tableBody">
                <!-- Table content will be populated by JavaScript -->
            </tbody>
        </table>
    </div>

    <script>
        // Global variables
        let opportunities = [];
        let scraperStatus = {
            iuk: 'idle',
            tender: 'idle'
        };

        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadScraperStatus();
            loadLatestData();
        });

        function updateStatus(scraper, status) {
            scraperStatus[scraper] = status;
            const indicator = document.getElementById(`${scraper}-status`);
            const button = document.getElementById(`${scraper}-btn`);
            
            indicator.className = `status-indicator status-${status}`;
            
            if (status === 'running') {
                button.disabled = true;
                button.textContent = 'Running...';
            } else {
                button.disabled = false;
                button.textContent = scraper === 'iuk' ? 'Run IUK Scraper' : 'Run Tender Scraper';
            }
            
            // Update run all button
            const runAllBtn = document.getElementById('run-all-btn');
            const anyRunning = Object.values(scraperStatus).includes('running');
            runAllBtn.disabled = anyRunning;
        }

        function addLog(scraperId, message) {
            const logElement = document.getElementById(`${scraperId}-logs`);
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function toggleLogs(logId) {
            const logElement = document.getElementById(logId);
            logElement.style.display = logElement.style.display === 'none' ? 'block' : 'none';
        }

        async function runIUKScraper() {
            updateStatus('iuk', 'running');
            addLog('iuk', 'Starting Innovate UK scraper...');

            try {
                const response = await fetch('/api/run-iuk-scraper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    updateStatus('iuk', 'success');
                    addLog('iuk', `IUK scraper completed: ${result.count} opportunities found`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateStatus('iuk', 'error');
                addLog('iuk', `Error: ${error.message}`);
            }
        }

        async function runTenderScraper() {
            updateStatus('tender', 'running');
            addLog('tender', 'Starting Net Zero tender scraper...');

            try {
                const response = await fetch('/api/run-tender-scraper', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    updateStatus('tender', 'success');
                    addLog('tender', `Tender scraper completed: ${result.count} opportunities found`);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateStatus('tender', 'error');
                addLog('tender', `Error: ${error.message}`);
            }
        }

        async function runAllScrapers() {
            addLog('iuk', 'Starting combined scraper run...');
            addLog('tender', 'Starting combined scraper run...');

            try {
                const response = await fetch('/api/run-all-scrapers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    updateStatus('iuk', 'success');
                    updateStatus('tender', 'success');

                    addLog('iuk', `Combined run completed: ${result.total_count} total opportunities`);
                    addLog('tender', `IUK: ${result.iuk_count}, Tenders: ${result.tender_count}`);

                    // Load the combined data
                    await loadLatestData();
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                updateStatus('iuk', 'error');
                updateStatus('tender', 'error');
                addLog('iuk', `Error: ${error.message}`);
                addLog('tender', `Error: ${error.message}`);
            }
        }

        async function simulateScraperRun(type) {
            // Simulate scraper execution time
            const delay = Math.random() * 3000 + 2000; // 2-5 seconds
            await new Promise(resolve => setTimeout(resolve, delay));
            
            // In a real implementation, this would execute the Python scripts
            // and return actual results
        }

        function loadScraperStatus() {
            // In a real implementation, this would load from scraper_status.json
            // For now, we'll use placeholder data
            const lastUpdated = localStorage.getItem('lastUpdated') || 'Never';
            document.getElementById('last-updated').textContent = `Last updated: ${lastUpdated}`;
        }

        async function loadLatestData() {
            try {
                const response = await fetch('/api/get-latest-data');
                if (response.ok) {
                    const data = await response.json();
                    opportunities = data.opportunities || [];
                    populateTable(opportunities);
                    updateSummaryStats();

                    if (data.last_updated) {
                        document.getElementById('last-updated').textContent = `Last updated: ${new Date(data.last_updated).toLocaleString()}`;
                    }
                } else {
                    console.log('No data available, loading sample data');
                    loadSampleData();
                }
            } catch (error) {
                console.log('Error loading data, using sample data:', error);
                loadSampleData();
            }
        }

        function loadSampleData() {
            // Sample data combining both sources
            opportunities = [
                {
                    id: "iuk_1",
                    title: "Clean Energy Innovation Challenge",
                    source: "innovate_uk",
                    is_sustainable: true,
                    funding_category: "£100k - £1M",
                    funding_amount: "£500,000",
                    organisation: "Innovate UK",
                    closes_date: "2025-08-15",
                    url: "https://example.com/opportunity1"
                },
                {
                    id: "tender_1",
                    title: "Net Zero Carbon Reduction Programme",
                    source: "net_zero_tenders",
                    is_sustainable: true,
                    funding_category: "£1M - £10M",
                    funding_amount: "£2,500,000",
                    buyer_name: "Department for Energy Security",
                    closes_date: "2025-07-30",
                    url: "https://example.com/tender1"
                },
                {
                    id: "iuk_2",
                    title: "Digital Health Innovation",
                    source: "innovate_uk",
                    is_sustainable: false,
                    funding_category: "Under £100k",
                    funding_amount: "£75,000",
                    organisation: "NHS Innovation",
                    closes_date: "2025-09-01",
                    url: "https://example.com/opportunity2"
                }
            ];
            
            populateTable(opportunities);
            updateSummaryStats();
            
            // Update last updated time
            const now = new Date().toLocaleString();
            document.getElementById('last-updated').textContent = `Last updated: ${now}`;
            localStorage.setItem('lastUpdated', now);
        }

        function populateTable(data = opportunities) {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            data.forEach(opp => {
                const row = document.createElement('tr');
                
                const sustainabilityClass = opp.is_sustainable ? 'sustainable-yes' : 'sustainable-no';
                const sustainabilityText = opp.is_sustainable ? 'Yes' : 'No';
                
                const sourceClass = opp.source === 'innovate_uk' ? 'source-iuk' : 'source-tender';
                const sourceText = opp.source === 'innovate_uk' ? 'Innovate UK' : 'Net Zero Tenders';
                
                let fundingClass = 'funding-none';
                if (opp.funding_category && opp.funding_category.includes('Over £10M')) fundingClass = 'funding-high';
                else if (opp.funding_category && opp.funding_category.includes('£1M - £10M')) fundingClass = 'funding-high';
                else if (opp.funding_category && opp.funding_category.includes('£100k - £1M')) fundingClass = 'funding-medium';
                else if (opp.funding_category && opp.funding_category.includes('Under £100k')) fundingClass = 'funding-low';

                const organisation = opp.organisation || opp.buyer_name || 'Not specified';
                const fundingAmount = opp.funding_amount || 'See details';
                const fundingCategory = opp.funding_category || 'No amount specified';

                row.innerHTML = `
                    <td class="title-cell"><span class="title-text">${opp.title}</span></td>
                    <td><span class="${sourceClass}">${sourceText}</span></td>
                    <td><span class="${sustainabilityClass}">${sustainabilityText}</span></td>
                    <td class="${fundingClass}">${fundingCategory}</td>
                    <td>${fundingAmount}</td>
                    <td>${organisation}</td>
                    <td>${opp.closes_date || 'Not specified'}</td>
                    <td class="url-cell"><a href="${opp.url}" target="_blank">View Details</a></td>
                `;
                
                tbody.appendChild(row);
            });

            document.getElementById('resultsCount').textContent = `Showing ${data.length} opportunities`;
        }

        function updateSummaryStats() {
            const totalCount = opportunities.length;
            const iukCount = opportunities.filter(opp => opp.source === 'innovate_uk').length;
            const tenderCount = opportunities.filter(opp => opp.source === 'net_zero_tenders').length;
            const sustainableCount = opportunities.filter(opp => opp.is_sustainable).length;
            
            document.getElementById('total-count').textContent = totalCount;
            document.getElementById('iuk-count').textContent = iukCount;
            document.getElementById('tender-count').textContent = tenderCount;
            document.getElementById('sustainable-count').textContent = sustainableCount;
        }

        function filterTable() {
            const sourceFilter = document.getElementById('sourceFilter').value;
            const sustainabilityFilter = document.getElementById('sustainabilityFilter').value;
            const fundingFilter = document.getElementById('fundingFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

            let filteredData = opportunities.filter(opp => {
                let matches = true;

                if (sourceFilter !== 'all' && opp.source !== sourceFilter) {
                    matches = false;
                }

                if (sustainabilityFilter !== 'all') {
                    if (sustainabilityFilter === 'yes' && !opp.is_sustainable) matches = false;
                    if (sustainabilityFilter === 'no' && opp.is_sustainable) matches = false;
                }

                if (fundingFilter !== 'all' && opp.funding_category !== fundingFilter) {
                    matches = false;
                }

                if (searchFilter && !opp.title.toLowerCase().includes(searchFilter)) {
                    matches = false;
                }

                return matches;
            });

            populateTable(filteredData);
        }

        function exportToCSV() {
            const table = document.getElementById('opportunitiesTable');
            const rows = table.querySelectorAll('tr');
            let csvContent = '';

            rows.forEach(row => {
                const cols = row.querySelectorAll('td, th');
                const rowData = Array.from(cols).map(col => {
                    let text = col.innerText;
                    // Handle URLs specially
                    if (col.querySelector('a')) {
                        text = col.querySelector('a').href;
                    }
                    // Escape quotes and wrap in quotes if contains comma
                    if (text.includes(',') || text.includes('"')) {
                        text = '"' + text.replace(/"/g, '""') + '"';
                    }
                    return text;
                });
                csvContent += rowData.join(',') + '\n';
            });

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'enhanced_funding_opportunities.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
